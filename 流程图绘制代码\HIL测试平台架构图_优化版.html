<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIL测试平台架构图 - 优化版</title>
    <style>
        /*
         * 优化版HIL测试平台架构图
         * 设计理念：简洁、方正、模块化
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 简化色彩体系 --- */
            --color-primary: #1e40af;
            --color-hardware: #059669;
            --color-simulation: #ea580c;
            --color-core: #7c3aed;
            
            --color-border: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .architecture-title {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
            text-align: center;
        }

        .architecture-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 40px;
            width: 700px;
            height: 600px;
            position: relative;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 20px;
            height: 100%;
            position: relative;
        }

        .module-card {
            background-color: var(--color-bg-main);
            border: 3px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: var(--shadow-subtle);
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .module-title {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 8px;
            color: var(--color-text-dark);
        }

        .module-subtitle {
            font-size: 0.8rem;
            color: var(--color-text-light);
            margin-bottom: 8px;
        }

        .module-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        /* 核心HIL平台 - 中心位置 */
        .hil-core {
            grid-column: 2;
            grid-row: 2;
            border-color: var(--color-core);
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(124, 58, 237, 0.05));
        }

        .hil-core .module-icon {
            color: var(--color-core);
        }

        .hil-core .module-title {
            color: var(--color-core);
            font-size: 1.1rem;
        }

        /* 硬件模块 */
        .hardware-module {
            border-color: var(--color-hardware);
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.05));
        }

        .hardware-module .module-icon {
            color: var(--color-hardware);
        }

        /* 仿真模块 */
        .simulation-module {
            border-color: var(--color-simulation);
            background: linear-gradient(135deg, rgba(234, 88, 12, 0.1), rgba(234, 88, 12, 0.05));
        }

        .simulation-module .module-icon {
            color: var(--color-simulation);
        }

        /* 监控模块 */
        .monitoring-module {
            border-color: var(--color-primary);
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.1), rgba(30, 64, 175, 0.05));
        }

        .monitoring-module .module-icon {
            color: var(--color-primary);
        }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .capabilities-summary {
            position: absolute;
            bottom: -100px;
            left: 0;
            right: 0;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            font-size: 0.75rem;
        }

        .capability-item {
            text-align: center;
            padding: 10px;
            background-color: var(--color-bg-main);
            border-radius: 8px;
            border: 1px solid var(--color-border);
        }

        .capability-value {
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: 4px;
        }

        .capability-label {
            color: var(--color-text-light);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .architecture-container {
                width: 90vw;
                height: 90vw;
                max-width: 600px;
                max-height: 600px;
            }
            
            .module-card {
                padding: 15px;
            }
            
            .module-title {
                font-size: 0.9rem;
            }
            
            .module-icon {
                font-size: 1.5rem;
            }
            
            .capabilities-summary {
                grid-template-columns: repeat(2, 1fr);
                bottom: -120px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        .module-card {
            animation: fadeIn 0.6s ease forwards;
        }

        .module-card:nth-child(1) { animation-delay: 0.1s; }
        .module-card:nth-child(2) { animation-delay: 0.2s; }
        .module-card:nth-child(3) { animation-delay: 0.3s; }
        .module-card:nth-child(4) { animation-delay: 0.4s; }
        .module-card:nth-child(5) { animation-delay: 0.5s; }
        .module-card:nth-child(6) { animation-delay: 0.6s; }
        .module-card:nth-child(7) { animation-delay: 0.7s; }
        .module-card:nth-child(8) { animation-delay: 0.8s; }
        .module-card:nth-child(9) { animation-delay: 0.9s; }

        /* 连接线动画 */
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .connection-line {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="architecture-title">HIL测试平台架构图</div>
    
    <div class="architecture-container">
        <div class="architecture-grid">
            <svg class="connector-canvas" id="connectorCanvas">
                <defs>
                    <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                            markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                            orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-primary)" />
                    </marker>
                </defs>
            </svg>

            <!-- 第一行：硬件层 -->
            <div class="module-card hardware-module">
                <div class="module-icon">🖥️</div>
                <div class="module-title">飞控计算机</div>
                <div class="module-subtitle">ARM A78</div>
            </div>

            <div class="module-card hardware-module">
                <div class="module-icon">📡</div>
                <div class="module-title">通信模块</div>
                <div class="module-subtitle">安全协议</div>
            </div>

            <div class="module-card hardware-module">
                <div class="module-icon">🛡️</div>
                <div class="module-title">赛安模组</div>
                <div class="module-subtitle">TEE隔离</div>
            </div>

            <!-- 第二行：核心平台 -->
            <div class="module-card simulation-module">
                <div class="module-icon">🌐</div>
                <div class="module-title">环境仿真</div>
                <div class="module-subtitle">6DOF模型</div>
            </div>

            <div class="module-card hil-core">
                <div class="module-icon">⚙️</div>
                <div class="module-title">HIL核心平台</div>
                <div class="module-subtitle">1KHz实时</div>
            </div>

            <div class="module-card simulation-module">
                <div class="module-icon">⚠️</div>
                <div class="module-title">威胁注入</div>
                <div class="module-subtitle">GPS欺骗</div>
            </div>

            <!-- 第三行：监控分析 -->
            <div class="module-card monitoring-module">
                <div class="module-icon">📊</div>
                <div class="module-title">性能监控</div>
                <div class="module-subtitle">实时指标</div>
            </div>

            <div class="module-card monitoring-module">
                <div class="module-icon">🔍</div>
                <div class="module-title">事件分析</div>
                <div class="module-subtitle">威胁检测</div>
            </div>

            <div class="module-card monitoring-module">
                <div class="module-icon">💾</div>
                <div class="module-title">数据记录</div>
                <div class="module-subtitle">回放分析</div>
            </div>
        </div>

        <!-- 测试能力摘要 -->
        <div class="capabilities-summary">
            <div class="capability-item">
                <div class="capability-value">1KHz</div>
                <div class="capability-label">飞控回路</div>
            </div>
            <div class="capability-item">
                <div class="capability-value">&lt;1ms</div>
                <div class="capability-label">系统延迟</div>
            </div>
            <div class="capability-item">
                <div class="capability-value">&gt;1Gbps</div>
                <div class="capability-label">数据吞吐</div>
            </div>
            <div class="capability-item">
                <div class="capability-value">100h+</div>
                <div class="capability-label">连续测试</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const svg = document.getElementById('connectorCanvas');
            const container = svg.parentElement;
            
            function drawConnections() {
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                
                const containerRect = container.getBoundingClientRect();
                const gridItems = container.querySelectorAll('.module-card');
                
                // 获取中心模块位置
                const coreModule = container.querySelector('.hil-core');
                const coreRect = coreModule.getBoundingClientRect();
                const coreX = coreRect.left - containerRect.left + coreRect.width / 2;
                const coreY = coreRect.top - containerRect.top + coreRect.height / 2;
                
                // 连接中心模块到周围模块
                gridItems.forEach((item, index) => {
                    if (item.classList.contains('hil-core')) return;
                    
                    const itemRect = item.getBoundingClientRect();
                    const itemX = itemRect.left - containerRect.left + itemRect.width / 2;
                    const itemY = itemRect.top - containerRect.top + itemRect.height / 2;
                    
                    // 绘制连接线
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', coreX);
                    line.setAttribute('y1', coreY);
                    line.setAttribute('x2', itemX);
                    line.setAttribute('y2', itemY);
                    line.setAttribute('stroke', 'var(--color-primary)');
                    line.setAttribute('stroke-width', '2');
                    line.setAttribute('opacity', '0.4');
                    line.classList.add('connection-line');
                    
                    svg.appendChild(line);
                });
            }

            // 添加交互效果
            const modules = document.querySelectorAll('.module-card');
            modules.forEach(module => {
                module.addEventListener('mouseenter', () => {
                    if (module.classList.contains('hil-core')) {
                        module.style.transform = 'scale(1.05)';
                        module.style.borderColor = 'var(--color-core)';
                    } else {
                        module.style.transform = 'translateY(-5px)';
                    }
                });
                
                module.addEventListener('mouseleave', () => {
                    module.style.transform = '';
                    module.style.borderColor = '';
                });
            });

            // 初始化连接线
            setTimeout(drawConnections, 200);
            
            // 响应式更新
            const observer = new ResizeObserver(drawConnections);
            observer.observe(container);
        });
    </script>
</body>
</html>