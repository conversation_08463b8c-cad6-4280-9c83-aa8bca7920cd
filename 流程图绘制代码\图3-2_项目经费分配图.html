<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图3-2 项目经费分配图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的饼图实现
         * 核心设计理念：SVG绘图 + 数据驱动 + 交互动画
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-primary: #1e40af; /* 主色调-蓝色 */
            --color-equipment: #059669; /* 设备费-绿色 */
            --color-business: #dc2626; /* 业务费-红色 */
            --color-labor: #7c3aed; /* 劳务费-紫色 */
            --color-other: #ea580c; /* 其他费用-橙色 */
            --color-management: #f59e0b; /* 管理费-黄色 */
            
            --color-border-light: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --shadow-strong: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .chart-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
        }

        .chart-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 40px;
            margin: 0 auto;
            max-width: 1200px;
            width: 100%;
        }

        .chart-main {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            align-items: center;
        }

        .pie-chart-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .pie-chart {
            width: 400px;
            height: 400px;
            transform: rotate(-90deg);
        }

        .pie-slice {
            cursor: pointer;
            transition: all 0.3s ease;
            stroke: white;
            stroke-width: 2;
        }

        .pie-slice:hover {
            filter: brightness(1.1);
            transform: scale(1.02);
            transform-origin: center;
        }

        .pie-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background-color: var(--color-bg-main);
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: var(--shadow-subtle);
        }

        .total-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .total-label {
            font-size: 0.9rem;
            color: var(--color-text-light);
            margin-top: 4px;
        }

        .legend-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background-color: var(--color-bg-light);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .legend-item:hover {
            background-color: var(--color-bg-main);
            box-shadow: var(--shadow-subtle);
            border-color: var(--color-border-light);
        }

        .legend-item.active {
            border-color: var(--color-primary);
            box-shadow: var(--shadow-strong);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            flex-shrink: 0;
        }

        .legend-content {
            flex: 1;
        }

        .legend-title {
            font-weight: 600;
            font-size: 1rem;
            color: var(--color-text-dark);
            margin-bottom: 4px;
        }

        .legend-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .legend-amount {
            font-size: 0.9rem;
            color: var(--color-text-light);
        }

        .legend-percentage {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-primary);
        }

        .breakdown-section {
            margin-top: 40px;
            padding: 30px;
            background-color: var(--color-bg-light);
            border-radius: var(--border-radius);
        }

        .breakdown-title {
            font-family: var(--font-heading);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--color-primary);
        }

        .breakdown-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .breakdown-item {
            background-color: var(--color-bg-main);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--color-primary);
        }

        .breakdown-item-title {
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .breakdown-color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .breakdown-list {
            list-style: none;
        }

        .breakdown-list li {
            padding: 4px 0;
            color: var(--color-text-light);
            font-size: 0.9rem;
            position: relative;
            padding-left: 16px;
        }

        .breakdown-list li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--color-primary);
        }

        .funding-source {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            border: 2px solid var(--color-primary);
        }

        .funding-title {
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .funding-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .funding-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: var(--color-bg-light);
            border-radius: 6px;
        }

        .funding-label {
            font-weight: 500;
            color: var(--color-text-dark);
        }

        .funding-amount {
            font-weight: 600;
            color: var(--color-primary);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .chart-main {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .pie-chart {
                width: 300px;
                height: 300px;
            }
            
            .pie-center {
                width: 100px;
                height: 100px;
            }
            
            .total-amount {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 768px) {
            .chart-container {
                padding: 20px;
            }
            
            .breakdown-grid {
                grid-template-columns: 1fr;
            }
            
            .funding-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .legend-item {
            animation: fadeIn 0.5s ease forwards;
        }

        .legend-item:nth-child(1) { animation-delay: 0.1s; }
        .legend-item:nth-child(2) { animation-delay: 0.2s; }
        .legend-item:nth-child(3) { animation-delay: 0.3s; }
        .legend-item:nth-child(4) { animation-delay: 0.4s; }
        .legend-item:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="chart-title">图3-2 项目经费分配图</div>
    
    <div class="chart-container">
        <div class="chart-main">
            <!-- 饼图 -->
            <div class="pie-chart-container">
                <svg class="pie-chart" id="pieChart">
                    <!-- 饼图切片将通过JavaScript生成 -->
                </svg>
                <div class="pie-center">
                    <div class="total-amount">700万</div>
                    <div class="total-label">总投资</div>
                </div>
            </div>

            <!-- 图例 -->
            <div class="legend-container" id="legendContainer">
                <!-- 图例项将通过JavaScript生成 -->
            </div>
        </div>

        <!-- 详细分解 -->
        <div class="breakdown-section">
            <div class="breakdown-title">经费详细分解</div>
            <div class="breakdown-grid">
                <div class="breakdown-item">
                    <div class="breakdown-item-title">
                        <div class="breakdown-color-indicator" style="background-color: var(--color-equipment);"></div>
                        设备费 (200万元)
                    </div>
                    <ul class="breakdown-list">
                        <li>HIL测试平台设备 (80万)</li>
                        <li>芯片设计开发工具 (60万)</li>
                        <li>测试仪器设备 (40万)</li>
                        <li>服务器及计算设备 (20万)</li>
                    </ul>
                </div>

                <div class="breakdown-item">
                    <div class="breakdown-item-title">
                        <div class="breakdown-color-indicator" style="background-color: var(--color-business);"></div>
                        业务费 (340万元)
                    </div>
                    <ul class="breakdown-list">
                        <li>芯片流片费用 (150万)</li>
                        <li>外协开发费用 (80万)</li>
                        <li>测试验证费用 (60万)</li>
                        <li>差旅会议费 (30万)</li>
                        <li>材料消耗费 (20万)</li>
                    </ul>
                </div>

                <div class="breakdown-item">
                    <div class="breakdown-item-title">
                        <div class="breakdown-color-indicator" style="background-color: var(--color-labor);"></div>
                        劳务费 (135万元)
                    </div>
                    <ul class="breakdown-list">
                        <li>核心技术人员 (80万)</li>
                        <li>测试验证人员 (30万)</li>
                        <li>项目管理人员 (15万)</li>
                        <li>外聘专家费用 (10万)</li>
                    </ul>
                </div>

                <div class="breakdown-item">
                    <div class="breakdown-item-title">
                        <div class="breakdown-color-indicator" style="background-color: var(--color-other);"></div>
                        其他费用 (10万元)
                    </div>
                    <ul class="breakdown-list">
                        <li>知识产权申请费 (5万)</li>
                        <li>标准编制费用 (3万)</li>
                        <li>不可预见费用 (2万)</li>
                    </ul>
                </div>

                <div class="breakdown-item">
                    <div class="breakdown-item-title">
                        <div class="breakdown-color-indicator" style="background-color: var(--color-management);"></div>
                        管理费 (15万元)
                    </div>
                    <ul class="breakdown-list">
                        <li>项目管理费 (10万)</li>
                        <li>财务管理费 (3万)</li>
                        <li>审计费用 (2万)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 资金来源 -->
        <div class="funding-source">
            <div class="funding-title">资金来源构成</div>
            <div class="funding-grid">
                <div class="funding-item">
                    <span class="funding-label">专项资助</span>
                    <span class="funding-amount">200万元 (28.6%)</span>
                </div>
                <div class="funding-item">
                    <span class="funding-label">企业自筹</span>
                    <span class="funding-amount">500万元 (71.4%)</span>
                </div>
                <div class="funding-item">
                    <span class="funding-label">总投资</span>
                    <span class="funding-amount">700万元 (100%)</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 经费数据
        const budgetData = [
            { name: '设备费', amount: 200, color: 'var(--color-equipment)', description: 'HIL平台、芯片工具、测试设备等' },
            { name: '业务费', amount: 340, color: 'var(--color-business)', description: '流片费用、外协开发、测试验证等' },
            { name: '劳务费', amount: 135, color: 'var(--color-labor)', description: '技术人员、测试人员、专家费用等' },
            { name: '其他费用', amount: 10, color: 'var(--color-other)', description: '知识产权、标准编制、不可预见费等' },
            { name: '管理费', amount: 15, color: 'var(--color-management)', description: '项目管理、财务管理、审计费用等' }
        ];

        const totalAmount = budgetData.reduce((sum, item) => sum + item.amount, 0);

        // 创建饼图
        function createPieChart() {
            const svg = document.getElementById('pieChart');
            const centerX = 200;
            const centerY = 200;
            const radius = 150;
            
            let currentAngle = 0;
            
            budgetData.forEach((item, index) => {
                const percentage = (item.amount / totalAmount) * 100;
                const angle = (item.amount / totalAmount) * 360;
                
                // 计算路径
                const startAngle = currentAngle;
                const endAngle = currentAngle + angle;
                
                const x1 = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
                const y1 = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
                const x2 = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
                const y2 = centerY + radius * Math.sin((endAngle * Math.PI) / 180);
                
                const largeArcFlag = angle > 180 ? 1 : 0;
                
                const pathData = [
                    `M ${centerX} ${centerY}`,
                    `L ${x1} ${y1}`,
                    `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                    'Z'
                ].join(' ');
                
                // 创建路径元素
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', pathData);
                path.setAttribute('fill', item.color);
                path.classList.add('pie-slice');
                path.setAttribute('data-index', index);
                
                // 添加事件监听
                path.addEventListener('mouseenter', () => highlightLegendItem(index));
                path.addEventListener('mouseleave', () => clearHighlight());
                path.addEventListener('click', () => toggleLegendItem(index));
                
                svg.appendChild(path);
                
                currentAngle += angle;
            });
        }

        // 创建图例
        function createLegend() {
            const container = document.getElementById('legendContainer');
            
            budgetData.forEach((item, index) => {
                const percentage = ((item.amount / totalAmount) * 100).toFixed(1);
                
                const legendItem = document.createElement('div');
                legendItem.classList.add('legend-item');
                legendItem.setAttribute('data-index', index);
                
                legendItem.innerHTML = `
                    <div class="legend-color" style="background-color: ${item.color};"></div>
                    <div class="legend-content">
                        <div class="legend-title">${item.name}</div>
                        <div class="legend-details">
                            <span class="legend-amount">${item.amount}万元 • ${item.description}</span>
                            <span class="legend-percentage">${percentage}%</span>
                        </div>
                    </div>
                `;
                
                // 添加事件监听
                legendItem.addEventListener('mouseenter', () => highlightPieSlice(index));
                legendItem.addEventListener('mouseleave', () => clearHighlight());
                legendItem.addEventListener('click', () => toggleLegendItem(index));
                
                container.appendChild(legendItem);
            });
        }

        // 高亮饼图切片
        function highlightPieSlice(index) {
            const slices = document.querySelectorAll('.pie-slice');
            slices.forEach((slice, i) => {
                if (i === index) {
                    slice.style.filter = 'brightness(1.2)';
                    slice.style.transform = 'scale(1.05)';
                } else {
                    slice.style.opacity = '0.7';
                }
            });
        }

        // 高亮图例项
        function highlightLegendItem(index) {
            const items = document.querySelectorAll('.legend-item');
            items.forEach((item, i) => {
                if (i === index) {
                    item.classList.add('active');
                } else {
                    item.style.opacity = '0.7';
                }
            });
        }

        // 清除高亮
        function clearHighlight() {
            const slices = document.querySelectorAll('.pie-slice');
            const items = document.querySelectorAll('.legend-item');
            
            slices.forEach(slice => {
                slice.style.filter = '';
                slice.style.transform = '';
                slice.style.opacity = '';
            });
            
            items.forEach(item => {
                item.classList.remove('active');
                item.style.opacity = '';
            });
        }

        // 切换图例项状态
        function toggleLegendItem(index) {
            const item = document.querySelector(`.legend-item[data-index="${index}"]`);
            item.classList.toggle('active');
            
            // 显示详细信息或执行其他操作
            console.log(`选中了: ${budgetData[index].name}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            createPieChart();
            createLegend();
        });
    </script>
</body>
</html>