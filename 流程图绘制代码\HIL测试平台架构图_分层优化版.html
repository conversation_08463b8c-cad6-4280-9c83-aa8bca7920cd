<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIL测试平台架构图 - 分层优化版</title>
    <style>
        /*
         * HIL测试平台架构图 - 分层优化版
         * 设计理念：清晰的层次划分 + 简洁的模块设计
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 分层色彩体系 --- */
            --color-primary: #1e40af;
            --color-monitoring: #059669;    /* 监控分析层-绿色 */
            --color-simulation: #ea580c;    /* 仿真环境层-橙色 */
            --color-hardware: #7c3aed;      /* 真实硬件层-紫色 */
            
            --color-border: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.1);
            --shadow-layer: 0 4px 12px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .architecture-title {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
            text-align: center;
        }

        .architecture-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 30px;
            width: 800px;
            position: relative;
        }

        .architecture-layer {
            margin-bottom: 25px;
            position: relative;
        }

        .architecture-layer:last-child {
            margin-bottom: 0;
        }

        .layer-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
        }

        .layer-icon {
            font-size: 1.2rem;
            margin-right: 8px;
        }

        .layer-modules {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            padding: 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-layer);
        }

        /* 监控分析层样式 */
        .monitoring-layer .layer-header {
            background: linear-gradient(135deg, var(--color-monitoring), #10b981);
            color: white;
        }

        .monitoring-layer .layer-modules {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.02));
            border: 2px solid rgba(5, 150, 105, 0.2);
        }

        /* 仿真环境层样式 */
        .simulation-layer .layer-header {
            background: linear-gradient(135deg, var(--color-simulation), #f97316);
            color: white;
        }

        .simulation-layer .layer-modules {
            background: linear-gradient(135deg, rgba(234, 88, 12, 0.05), rgba(234, 88, 12, 0.02));
            border: 2px solid rgba(234, 88, 12, 0.2);
        }

        /* 真实硬件层样式 */
        .hardware-layer .layer-header {
            background: linear-gradient(135deg, var(--color-hardware), #a855f7);
            color: white;
        }

        .hardware-layer .layer-modules {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(124, 58, 237, 0.02));
            border: 2px solid rgba(124, 58, 237, 0.2);
        }

        .module-card {
            background-color: var(--color-bg-main);
            border: 2px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: var(--shadow-subtle);
            min-height: 120px;
            justify-content: center;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .module-title {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 8px;
            color: var(--color-text-dark);
        }

        .module-content {
            list-style: none;
            font-size: 0.8rem;
            color: var(--color-text-light);
            line-height: 1.4;
        }

        .module-content li {
            position: relative;
            margin-bottom: 3px;
            padding-left: 12px;
        }

        .module-content li::before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        /* 不同层的模块样式 */
        .monitoring-layer .module-content li::before {
            color: var(--color-monitoring);
        }

        .simulation-layer .module-content li::before {
            color: var(--color-simulation);
        }

        .hardware-layer .module-content li::before {
            color: var(--color-hardware);
        }

        .monitoring-layer .module-card:hover {
            border-color: var(--color-monitoring);
        }

        .simulation-layer .module-card:hover {
            border-color: var(--color-simulation);
        }

        .hardware-layer .module-card:hover {
            border-color: var(--color-hardware);
        }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .capabilities-summary {
            margin-top: 25px;
            padding: 20px;
            background: linear-gradient(135deg, var(--color-bg-light), var(--color-bg-main));
            border-radius: var(--border-radius);
            border: 2px solid var(--color-primary);
        }

        .capabilities-title {
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: 15px;
            text-align: center;
            font-size: 1.1rem;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .capability-item {
            text-align: center;
            padding: 12px;
            background-color: var(--color-bg-main);
            border-radius: 8px;
            border: 1px solid var(--color-border);
            box-shadow: var(--shadow-subtle);
        }

        .capability-value {
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: 4px;
            font-size: 0.9rem;
        }

        .capability-label {
            color: var(--color-text-light);
            font-size: 0.8rem;
        }

        /* 响应式设计 */
        @media (max-width: 900px) {
            .architecture-container {
                width: 95vw;
                max-width: 800px;
            }
            
            .layer-modules {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .capabilities-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .layer-modules {
                grid-template-columns: 1fr;
            }
            
            .capabilities-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .architecture-layer {
            animation: fadeInUp 0.6s ease forwards;
        }

        .architecture-layer:nth-child(1) { animation-delay: 0.1s; }
        .architecture-layer:nth-child(2) { animation-delay: 0.3s; }
        .architecture-layer:nth-child(3) { animation-delay: 0.5s; }

        /* 层间连接线动画 */
        @keyframes connectionFlow {
            0% { stroke-dashoffset: 20; }
            100% { stroke-dashoffset: 0; }
        }

        .connection-line {
            stroke-dasharray: 8, 4;
            animation: connectionFlow 3s linear infinite;
        }
    </style>
</head>
<body>
    <div class="architecture-title">HIL测试平台系统架构图</div>
    
    <div class="architecture-container">
        <svg class="connector-canvas" id="connectorCanvas">
            <defs>
                <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-primary)" />
                </marker>
            </defs>
        </svg>

        <!-- 监控分析层 -->
        <div class="architecture-layer monitoring-layer" id="monitoringLayer">
            <div class="layer-header">
                <span class="layer-icon">📊</span>
                <span>监控分析层</span>
            </div>
            <div class="layer-modules">
                <div class="module-card">
                    <div class="module-title">性能实时监控</div>
                    <ul class="module-content">
                        <li>延迟/吞吐量</li>
                        <li>系统状态</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">安全事件分析</div>
                    <ul class="module-content">
                        <li>威胁检测评估</li>
                        <li>响应时间</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">数据记录与回放</div>
                    <ul class="module-content">
                        <li>测试数据管理</li>
                        <li>故障回放</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 仿真环境层 -->
        <div class="architecture-layer simulation-layer" id="simulationLayer">
            <div class="layer-header">
                <span class="layer-icon">🌐</span>
                <span>仿真环境层</span>
            </div>
            <div class="layer-modules">
                <div class="module-card">
                    <div class="module-title">飞行动力学仿真</div>
                    <ul class="module-content">
                        <li>6DOF模型</li>
                        <li>气动环境模型</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">传感器信号仿真</div>
                    <ul class="module-content">
                        <li>GPS/IMU信号</li>
                        <li>视觉/雷达数据</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">复杂威胁环境仿真</div>
                    <ul class="module-content">
                        <li>GPS欺骗</li>
                        <li>网络攻击/数据劫持</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 真实硬件层 -->
        <div class="architecture-layer hardware-layer" id="hardwareLayer">
            <div class="layer-header">
                <span class="layer-icon">🔧</span>
                <span>真实硬件层</span>
            </div>
            <div class="layer-modules">
                <div class="module-card">
                    <div class="module-title">飞控计算机 (FCC)</div>
                    <ul class="module-content">
                        <li>运行真实固件</li>
                        <li>1KHz实时控制</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">"赛安"安全模组</div>
                    <ul class="module-content">
                        <li>硬件加密/解密</li>
                        <li>威胁检测/响应隔离</li>
                    </ul>
                </div>
                <div class="module-card">
                    <div class="module-title">通信模块 (CM)</div>
                    <ul class="module-content">
                        <li>处理真实协议</li>
                        <li>安全通信</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 测试能力摘要 -->
        <div class="capabilities-summary">
            <div class="capabilities-title">🚀 核心测试能力</div>
            <div class="capabilities-grid">
                <div class="capability-item">
                    <div class="capability-value">1KHz</div>
                    <div class="capability-label">飞控回路频率</div>
                </div>
                <div class="capability-item">
                    <div class="capability-value">&lt;1ms</div>
                    <div class="capability-label">系统延迟</div>
                </div>
                <div class="capability-item">
                    <div class="capability-value">&gt;1Gbps</div>
                    <div class="capability-label">数据吞吐量</div>
                </div>
                <div class="capability-item">
                    <div class="capability-value">100h+</div>
                    <div class="capability-label">连续稳定测试</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const svg = document.getElementById('connectorCanvas');
            const container = svg.parentElement;
            
            function drawLayerConnections() {
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                
                const containerRect = container.getBoundingClientRect();
                const layers = container.querySelectorAll('.architecture-layer');
                
                // 绘制层间连接线
                for (let i = 0; i < layers.length - 1; i++) {
                    const currentLayer = layers[i];
                    const nextLayer = layers[i + 1];
                    
                    const currentRect = currentLayer.getBoundingClientRect();
                    const nextRect = nextLayer.getBoundingClientRect();
                    
                    const currentBottom = currentRect.bottom - containerRect.top;
                    const nextTop = nextRect.top - containerRect.top;
                    const centerX = (currentRect.left + currentRect.right) / 2 - containerRect.left;
                    
                    // 双向箭头连接
                    const downArrow = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    downArrow.setAttribute('x1', centerX - 20);
                    downArrow.setAttribute('y1', currentBottom + 5);
                    downArrow.setAttribute('x2', centerX - 20);
                    downArrow.setAttribute('y2', nextTop - 5);
                    downArrow.setAttribute('stroke', 'var(--color-primary)');
                    downArrow.setAttribute('stroke-width', '2');
                    downArrow.setAttribute('marker-end', 'url(#arrowhead)');
                    downArrow.setAttribute('opacity', '0.6');
                    downArrow.classList.add('connection-line');
                    
                    const upArrow = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    upArrow.setAttribute('x1', centerX + 20);
                    upArrow.setAttribute('y1', nextTop - 5);
                    upArrow.setAttribute('x2', centerX + 20);
                    upArrow.setAttribute('y2', currentBottom + 5);
                    upArrow.setAttribute('stroke', 'var(--color-primary)');
                    upArrow.setAttribute('stroke-width', '2');
                    upArrow.setAttribute('marker-end', 'url(#arrowhead)');
                    upArrow.setAttribute('opacity', '0.6');
                    upArrow.classList.add('connection-line');
                    
                    svg.appendChild(downArrow);
                    svg.appendChild(upArrow);
                }
            }

            // 添加交互效果
            const modules = document.querySelectorAll('.module-card');
            modules.forEach(module => {
                module.addEventListener('mouseenter', () => {
                    module.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                module.addEventListener('mouseleave', () => {
                    module.style.transform = '';
                });
            });

            // 初始化连接线
            setTimeout(drawLayerConnections, 300);
            
            // 响应式更新
            const observer = new ResizeObserver(drawLayerConnections);
            observer.observe(container);
        });
    </script>
</body>
</html>