<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图3-1 项目实施时间线图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的甘特图实现
         * 核心设计理念：CSS Grid布局 + 数据驱动 + 响应式设计
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-primary: #1e40af; /* 主色调-蓝色 */
            --color-phase1: #059669; /* 阶段一-绿色 */
            --color-phase2: #dc2626; /* 阶段二-红色 */
            --color-phase3: #7c3aed; /* 阶段三-紫色 */
            --color-executing: #374151; /* 执行期-深灰 */
            --color-preparing: #d1d5db; /* 准备期-浅灰 */
            --color-milestone: #f59e0b; /* 里程碑-橙色 */
            
            --color-border-light: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --border-radius: 6px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
        }

        .gantt-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--color-primary);
        }

        .gantt-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 30px;
            margin: 0 auto;
            max-width: 1400px;
            overflow-x: auto;
        }

        .gantt-chart {
            display: grid;
            grid-template-columns: 250px repeat(24, 1fr);
            gap: 1px;
            background-color: var(--color-border-light);
            border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius);
            min-width: 1200px;
        }

        .gantt-header {
            background-color: var(--color-primary);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .gantt-header.task-header {
            text-align: left;
            padding-left: 16px;
        }

        .gantt-row {
            display: contents;
        }

        .task-name {
            background-color: var(--color-bg-main);
            padding: 12px 16px;
            border-right: 1px solid var(--color-border-light);
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .phase-header {
            background-color: var(--color-bg-light);
            font-weight: 600;
            color: var(--color-primary);
        }

        .task-cell {
            background-color: var(--color-bg-main);
            position: relative;
            height: 40px;
            border-right: 1px solid var(--color-border-light);
        }

        .task-bar {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            height: 20px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: white;
            font-weight: 500;
        }

        .executing {
            background-color: var(--color-executing);
        }

        .preparing {
            background-color: var(--color-preparing);
            color: var(--color-text-dark);
        }

        .phase1 { background-color: var(--color-phase1); }
        .phase2 { background-color: var(--color-phase2); }
        .phase3 { background-color: var(--color-phase3); }

        .milestone {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background-color: var(--color-milestone);
            transform: translateY(-50%) rotate(45deg);
            border: 2px solid white;
        }

        .legend {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--color-bg-light);
            border-radius: var(--border-radius);
        }

        .legend-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-primary);
        }

        .legend-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        .legend-symbol {
            width: 20px;
            height: 12px;
            border-radius: 3px;
        }

        .legend-milestone {
            width: 12px;
            height: 12px;
            background-color: var(--color-milestone);
            transform: rotate(45deg);
            border: 2px solid white;
        }

        .milestones-section {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            border: 1px solid var(--color-border-light);
        }

        .milestones-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-primary);
        }

        .milestone-list {
            list-style: none;
        }

        .milestone-item {
            padding: 8px 0;
            border-bottom: 1px solid var(--color-border-light);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .milestone-item:last-child {
            border-bottom: none;
        }

        .milestone-marker {
            width: 8px;
            height: 8px;
            background-color: var(--color-milestone);
            transform: rotate(45deg);
            flex-shrink: 0;
        }

        .resources-section {
            margin-top: 30px;
            padding: 20px;
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            border: 1px solid var(--color-border-light);
        }

        .resources-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-primary);
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .resource-item {
            padding: 15px;
            background-color: var(--color-bg-light);
            border-radius: var(--border-radius);
        }

        .resource-label {
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: 5px;
        }

        .resource-content {
            color: var(--color-text-light);
            font-size: 0.9rem;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .gantt-container {
                padding: 20px;
            }
            
            .gantt-chart {
                grid-template-columns: 200px repeat(24, 30px);
            }
            
            .gantt-header {
                font-size: 0.8rem;
                padding: 8px 4px;
            }
            
            .task-name {
                font-size: 0.8rem;
                padding: 8px 12px;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background-color: white;
                padding: 1rem;
            }
            
            .gantt-container {
                box-shadow: none;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="gantt-title">图3-1 项目实施时间线图</div>
    
    <div class="gantt-container">
        <div class="gantt-chart" id="ganttChart">
            <!-- 表头 -->
            <div class="gantt-header task-header">任务阶段</div>
            <div class="gantt-header">1</div>
            <div class="gantt-header">2</div>
            <div class="gantt-header">3</div>
            <div class="gantt-header">4</div>
            <div class="gantt-header">5</div>
            <div class="gantt-header">6</div>
            <div class="gantt-header">7</div>
            <div class="gantt-header">8</div>
            <div class="gantt-header">9</div>
            <div class="gantt-header">10</div>
            <div class="gantt-header">11</div>
            <div class="gantt-header">12</div>
            <div class="gantt-header">13</div>
            <div class="gantt-header">14</div>
            <div class="gantt-header">15</div>
            <div class="gantt-header">16</div>
            <div class="gantt-header">17</div>
            <div class="gantt-header">18</div>
            <div class="gantt-header">19</div>
            <div class="gantt-header">20</div>
            <div class="gantt-header">21</div>
            <div class="gantt-header">22</div>
            <div class="gantt-header">23</div>
            <div class="gantt-header">24</div>

            <!-- 阶段一：组件级验证 -->
            <div class="gantt-row">
                <div class="task-name phase-header">阶段一：组件级验证</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- RT-TEE性能验证 -->
            <div class="gantt-row">
                <div class="task-name">├─ RT-TEE性能验证</div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 国密算法验证 -->
            <div class="gantt-row">
                <div class="task-name">├─ 国密算法验证</div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- AI引擎优化 -->
            <div class="gantt-row">
                <div class="task-name">├─ AI引擎优化</div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase1" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase1" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 阶段一验收 -->
            <div class="gantt-row">
                <div class="task-name">└─ 阶段一验收</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="milestone"></div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 阶段二：HIL集成测试 -->
            <div class="gantt-row">
                <div class="task-name phase-header">阶段二：HIL集成测试</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- HIL平台搭建 -->
            <div class="gantt-row">
                <div class="task-name">├─ HIL平台搭建</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 硬件集成 -->
            <div class="gantt-row">
                <div class="task-name">├─ 硬件集成</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 威胁场景测试 -->
            <div class="gantt-row">
                <div class="task-name">├─ 威胁场景测试</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 安全响应验证 -->
            <div class="gantt-row">
                <div class="task-name">├─ 安全响应验证</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase2" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase2" style="left: 0; width: 100%;">████</div></div>
            </div>

            <!-- 阶段二验收 -->
            <div class="gantt-row">
                <div class="task-name">└─ 阶段二验收</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div>
                <div class="task-cell"><div class="milestone"></div></div>
                <div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 阶段三：外场验证 -->
            <div class="gantt-row">
                <div class="task-name phase-header">阶段三：外场验证</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 样机开发 -->
            <div class="gantt-row">
                <div class="task-name">├─ 样机开发</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 联调测试 -->
            <div class="gantt-row">
                <div class="task-name">├─ 联调测试</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"></div><div class="task-cell"></div>
            </div>

            <!-- 外场验证 -->
            <div class="gantt-row">
                <div class="task-name">├─ 外场验证</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
            </div>

            <!-- 标准编制 -->
            <div class="gantt-row">
                <div class="task-name">├─ 标准编制</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar preparing phase3" style="left: 0; width: 100%;">░░</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
                <div class="task-cell"><div class="task-bar executing phase3" style="left: 0; width: 100%;">████</div></div>
            </div>

            <!-- 项目验收 -->
            <div class="gantt-row">
                <div class="task-name">└─ 项目验收</div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"></div><div class="task-cell"></div><div class="task-cell"></div>
                <div class="task-cell"><div class="milestone"></div></div>
            </div>
        </div>

        <!-- 图例 -->
        <div class="legend">
            <div class="legend-title">图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-symbol executing"></div>
                    <span>████ 执行期</span>
                </div>
                <div class="legend-item">
                    <div class="legend-symbol preparing"></div>
                    <span>░░░░ 准备期</span>
                </div>
                <div class="legend-item">
                    <div class="legend-milestone"></div>
                    <span>◆ 里程碑</span>
                </div>
                <div class="legend-item">
                    <div class="legend-symbol phase1"></div>
                    <span>阶段一：组件级验证</span>
                </div>
                <div class="legend-item">
                    <div class="legend-symbol phase2"></div>
                    <span>阶段二：HIL集成测试</span>
                </div>
                <div class="legend-item">
                    <div class="legend-symbol phase3"></div>
                    <span>阶段三：外场验证</span>
                </div>
            </div>
        </div>

        <!-- 关键里程碑 -->
        <div class="milestones-section">
            <div class="milestones-title">关键里程碑</div>
            <ul class="milestone-list">
                <li class="milestone-item">
                    <div class="milestone-marker"></div>
                    <span><strong>M1：组件级验证完成</strong> (第8个月末) - RT-TEE、国密算法、AI引擎核心技术验证完成</span>
                </li>
                <li class="milestone-item">
                    <div class="milestone-marker"></div>
                    <span><strong>M2：HIL集成测试完成</strong> (第14个月末) - 硬件在环测试平台验证完成</span>
                </li>
                <li class="milestone-item">
                    <div class="milestone-marker"></div>
                    <span><strong>M3：样机开发完成</strong> (第20个月末) - 无人机安全防护样机开发完成</span>
                </li>
                <li class="milestone-item">
                    <div class="milestone-marker"></div>
                    <span><strong>M4：外场验证完成</strong> (第22个月末) - 实际环境下的外场验证测试完成</span>
                </li>
                <li class="milestone-item">
                    <div class="milestone-marker"></div>
                    <span><strong>M5：项目验收交付</strong> (第24个月末) - 项目最终验收和成果交付</span>
                </li>
            </ul>
        </div>

        <!-- 资源配置 -->
        <div class="resources-section">
            <div class="resources-title">资源配置</div>
            <div class="resources-grid">
                <div class="resource-item">
                    <div class="resource-label">项目团队</div>
                    <div class="resource-content">8人核心团队 + 5人支撑团队<br>包含芯片设计、软件开发、系统集成、测试验证等专业人员</div>
                </div>
                <div class="resource-item">
                    <div class="resource-label">关键设备</div>
                    <div class="resource-content">HIL测试平台、无人机验证平台、测试仪器<br>芯片设计工具、服务器集群、专业测试设备</div>
                </div>
                <div class="resource-item">
                    <div class="resource-label">预算分配</div>
                    <div class="resource-content">组件验证30% + HIL测试40% + 外场验证30%<br>总投资700万元，专项资助200万元</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>