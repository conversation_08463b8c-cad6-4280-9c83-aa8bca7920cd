# 面向无人机系统的云边端协同可信安全防护技术研究与应用-陕西省重点研发计划项目申请书

## 一、基本信息

- **受理编号**:
- **管理类型**:
- **项目类**:
- **项目类别**: 重点项目-关键核心技术攻关-未来新兴产业领域
- **指南方向**: 3.无人自主与人机协同_003-3.5；面向无人机系统的云边端协同可信安全防护技术_005
- **项目名称**: 面向无人机系统的云边端协同可信安全防护技术研究与应用
- **申请单位**: 西安文渊软件科技有限公司（盖章）
- **项目负责人**: 赵豫平
- **手机号码**: 13679105656
- **推荐部门**: 西安市科学技术局
- **申请日期**: 年月日
- **电子邮箱**: <EMAIL>
- **办公电话**: 029-81884939
- **制作单位**: 陕西省科学技术厅

## 二、填报说明

### （一）项目经费预算表和预算科目

1. **直接费用**：指在课题研究开发过程中发生的与之直接相关的费用，主要包括设备费、业务费（含材料费、测试化验加工费、燃料动力费、差旅费、会议费、国际合作与交流费、出版/文献/信息传播/知识产权事务费、其他费用）和劳务费（含专家咨询费、雇佣人员劳务费、其他劳务费）。

   - **设备费**：指项目实施过程中必须购置的专用仪器设备，对现有仪器设备进行升级改造，以及租赁外单位仪器设备而发生的费用。购置单台价值在50万元以上（含50万元）的仪器设备需填写《50万以上设备明细表》。
   - **业务费**：指研究中除设备费、劳务费以外的其他直接费用，包括以下费用：
     - 材料费：指科技项目研究开发或科技创新体系建设过程中所支付的原材料、燃料动力、低值易耗品等的采购及运输、装卸、整理等费用。
     - 测试化验加工费：指项目实施过程中支付给外单位（包括项目承担单位内部独立经济核算单位）的试验、加工、测试、化验等费用。
     - 燃料动力费：是指在课题研究开发过程中相关大型仪器设备、专用科学装置等运行发生的可以单独计量的水、电、气、燃料消耗费用等。
     - 差旅费：指在科技项目研究开发或科技创新体系建设过程中，为科技项目研究开发或科技创新体系建设而进行国内调研考察、现场试验、学术交流等工作所发生的交通、住宿等费用。
     - 会议费：指科技项目研究开发或科技创新体系建设过程中为组织开展学术研讨、咨询以及协调项目等活动而发生的会议费用。
     - 国际合作与交流费：是指在课题研究开发过程中课题研究人员出国及外国专家来华工作的费用。国际合作与交流费应当严格执行国家外事经费管理的有关规定，不同的国家的补助标准不一样，请参考目的地国家具体补助标准。
     - 出版/文献/信息传播/知识产权事务费：指科技项目研究开发或科技创新体系建设过程中发生的信息检索费、著作出版印刷费、专用软件购买、论文版面费、数据调查费、专业通信费、知识产权事务费等费用。
     - 其他费用：指除上述费用之外与科技项目研究开发或科技创新体系建设有关的其他费用。
   - **劳务费**：指研究中支付给个人的直接费用包括专家咨询费、人员劳务费等：
     - 专家咨询费：指项目研究中支付给临时聘请专家的费用。
     - 聘用人员劳务费：指项目研究中支付给聘用人员的劳务费。
     - 其他劳务费：指项目给参与研究的研究生、博士后等人员支付的劳务费。
2. **间接费用**：指承担课题任务的单位在组织实施课题过程中发生的无法在直接费用中列支的相关费用。主要包括承担课题任务的单位为课题研究提供的现有仪器设备及房屋，水、电、气、暖消耗，有关管理费用的补助支出，以及绩效支出等。间接费用使用分段超额累退比例法计算并实行总额控制，按照不超过课题经费中直接费用扣除设备购置费后的一定比例核定，具体比例如下：

   - 100万元以下（含100万）部分不超过35%；
   - 100万元至500万元（含500万）的部分不超过30%；
   - 500万元至1000万元（含1000万）的部分不超过25%；
   - 超过1000万元以上的部分不超过20%。
   - 管理费：指科技项目承担单位及受托管理单位为组织管理科技项目而支出的相关费用。包括现有仪器设备和房屋使用费或折旧、直接管理人员费用和其他相关管理支出。
   - 绩效支出：是指承担课题任务的单位为提高科研工作绩效安排的相关支出。

## 三、单位基本情况

| 单位名称             | 西安文渊软件科技有限公司                                                  |
| -------------------- | ------------------------------------------------------------------------- |
| 注册所在地           | 西安市-雁塔区                                                             |
| 单位地址             | 陕西省西安市高新区高新一路25号创新大厦N302室                              |
| 单位所在地推荐部门   | 西安市科学技术局                                                          |
| 注册资本             | 1000.00万人民币                                                           |
| 单位隶属             | 市级                                                                      |
| 注册类型             | 企业单位-内资企业-私营企业                                                |
| 注册日期             | 2016-10-18                                                                |
| 所属国民经济行业     | 信息传输、软件和信息技术服务业-软件和信息技术服务业-软件开发-应用软件开发 |
| 企业规模             | 小型企业                                                                  |
| 单位特性             | 高新技术企业，科技型中小企业                                              |
| 单位资质             | ISO9000系列认证                                                           |
| 单位拥有研发机构状况 | 无                                                                        |
| 单位所在重点园区     | 国家级高新区-西安高新技术产业开发区                                       |

| 单位人员情况            |    |                         |        |                      |
| ----------------------- | -- | ----------------------- | ------ | -------------------- |
| 员工人数                | 17 | 其中:直接从事研发人员数 | 10     | 占员工总数比例58.82% |
| 其中:本科以上研发人员数 | 4  | 占员工总数比例          | 23.52% |                      |

## 四、项目基本情况

| 指南方向       | 3.无人自主与人机协同_003-3.5 面向无人机系统的云边端协同可信安全防护技术_005             |
| -------------- | --------------------------------------------------------------------------------------- |
| 项目名称       | 面向无人机系统的云边端协同可信安全防护技术研究与应用                                    |
| 所属重点产业链 | 半导体及集成电路                                                                        |
| 项目实施地点   | 西安                                                                                    |
| 开始日期       | 2025-06-01                                                                              |
| 结束日期       | 2027-05-30                                                                              |
| 经济行业领域   | 试验发展；科学研究和技术服务业-研究和试验发展-工程和技术研究和试验发展-工程和技术研究和 |
| 细分领域1      | 计算机科学技术基础学科520.10-数据安全与计算机安全520.1060                               |
| 细分领域2      | 半导体技术510.30-集成电路技术510.3040                                                   |
| 项目负责人     | 赵豫平                                                                                  |
| 电子邮箱       | <EMAIL>                                                         |
| 手机号码       | 13679105656                                                                             |
| 办公电话       | 029-81884939                                                                            |

| 序号 | 依托国家或省级基地平台 | 平台名称                                             | 依托协议和平台资质     |
| ---- | ---------------------- | ---------------------------------------------------- | ---------------------- |
| 1    | 工程研究中心           | 西北大学新型网络智能信息服务国家地方联合工程研究中心 | 合作协议与资质证明.pdf |

| 查新关键词 (用英文分号分开,3-5个) | 无人机专用安全管控芯片;可信嵌入式操作系统;安全私有传输协议;硬件级电子围栏;加解密算法硬件加速; |
| --------------------------------- | --------------------------------------------------------------------------------------------- |

| 序号 | 国别 | 省份   | 单位名称                 | 统一社会信用代码   | 单位地址                                             | 联系人 | 联系电话    | 合作协议                             |
| ---- | ---- | ------ | ------------------------ | ------------------ | ---------------------------------------------------- | ------ | ----------- | ------------------------------------ |
|      | 中国 | 浙江省 | 西交网络空间安全研究院   | 12330681MB1L38368J | 诸暨市浦阳路18号科创园3#楼                           | 宫海天 | 15776745870 | 西交网络空间研究院合作协议.pdf       |
| 2    | 中国 | 浙江省 | 浙江唯识智能科技有限公司 | 91330681MACTR8PT0N | 浙江省诸暨市暨阳街道兴农路9号数智安防产业园3幢1701室 | 唐凯   | 18950016767 | 浙江唯识智能科技有限公司合作协议.pdf |
| 3    | 中国 | 陕西省 | 西北大学                 | 126100004352012743 | 西安市太白路229号                                    | 贺小伟 | 13891851598 | 合作协议.pdf                         |

## 五、项目概况

### （一）项目主要研究内容与任务

基于零信任理念，研究自主可控的用于无人机可信执行环境的芯片级安全防护体系，从硬件层面保护数据资源受到的非授权访问、防篡改或盗取、对无人机飞行控制异常检测、抵御欺骗攻击和重放攻击等攻击。芯片具备高密文数据吞吐、独特安全存储、安全访问，国际/国密算法的硬件加速等创新设计，与自研可信嵌入式操作系统共同支撑无人机系统可信执行环境的安全防护，基于固有安全技术的轻量级可执行安全管控机制，可支撑实现无人机系统设备安全多要素和网络空间安全，并使TEE系统轻量化。

1. **创新技术一:芯片加密算法硬件加速**：内置算法硬件加速器设计，在较低的CPU占用率下实现加速算法数据吞吐量和加解密运算性能。
2. **创新技术二:高限制写存贮区保护机制**：建立OTP永久不可更改存贮空间和高限制性写存贮空间，贮存策略信息、敏感数据信息和黑/白名单等。
3. **创新技术三:动态真随机发生器**：芯片内置专用电路实现，低延迟和高吞吐量，解决了软件PRNG的熵不足和可预测性问题，物理隔离显著提升了抗攻击能力。
4. **创新技术四:独立时间防护机制**：独立电源域和独立复位域技术实现芯片内部计时的独立性和保护，支撑硬件级电子围栏等安全功能。
5. **创新技术五:可信安全管控系统**：包括自研嵌入式可信安全操作系统和安全私有传输协议。
6. **创新技术六:基于芯片国密算法的安全启动**：SM2-1椭圆曲线数字签名算法固件签名验证，SM3替代传统SHA-256进行完整性校验；SM4分段加密验签，支持大容量固件流式验证；实现一芯一密安全启动。
7. **创新技术七:高成熟度的可信安全防护技术验证平台**：芯片级安全可信系统与无人机企业的传统管理系统相结合，数据采集、监控、飞行日志、安全防护技术验证等管理。

### （二）产学研结合情况

项目依托西交网安研究院安全管控芯片、嵌入式操作系统的技术积累，主导研究硬件级可信执行环境构建，为无人机数据安全与实时防护提供底层支撑；西北大学新型网络智能信息服务国家地方联合工程研究中心聚焦安全协议形式化验证，提供理论模型与实验环境，完成安全私有传输协议的逻辑完备性与抗攻击能力；文渊公司联合浙江唯识智能公司负责技术方案整合，将芯片级可信执行环境与无人机系统集成，将系统平台升级为协同可信的安全防护技术验证平台。唯识智能公司负责产品安全升级，其他企业配合。唯识智能公司和文渊公司负责技术和产品产业化。

### （三）预期成果与经济、社会效益

1. **技术经济指标**：完成芯片级安全系统的开发，包括自主可控安全芯片、可信嵌入式操作系统和针对无人机通信协议的自动化验证。达成包括安全管控芯片硬件加速、安全访问设计、安全通信协议、可信执行环境和综合安全防护五项技术考核指标。基于项目技术成果，项目计划三年内完成30,000台无人机安全技术升级，2026年营收目标5000万-8000万元，2030年突破2亿元。
2. **促进企业创新和产业收入情况**

   - 产品升级：推出高安全等级物流无人机WS30（动态加密、抗干扰通信）、智能安防巡逻无人机JDYAir Pro（集成TEE与AI决策模块），覆盖物流、应急等场景。
   - 技术融合：将自主控制算法、低空算法仓与安全协议结合，构建“端-边-云”三位一体安全架构，降低黑飞风险30%。
   - 模式创新：建立“硬件销售-系统订阅-数据增值”多元收入模型，客单价提升至500万-1000万元（城市级合作）。通过政务无人机、网格化自动机场、数据中台等产品，覆盖公安、消防、农业等领域，落地案例突破300个。
   - 市场竞争力：依靠项目成果安全专利提升竞标竞争力。
3. **项目、人才、基地统筹**

   - 在西交网络空间安全研究院、西北大学设立联合实验室，共享芯片开发平台、协议验证工具及无人机仿真测试环境；基地配备10名专职科研人员，开展“云边端协同可信安全防护技术”研究。
   - 人才培养机制：开设校企联合课程“无人机安全技术”，每年培养20名复合型人才；每年提供10个实习岗位，学生参与安全验证平台搭建、芯片集成测试等实际项目；设立专项奖学金，选拔10名优秀学生深度参与技术攻关。
   - 资源协同：西交研究院主导硬件级TEE研发，西北大学提供协议验证工具，文渊公司整合方案，唯识智能推动产业化；每年组织2次产学研技术交流会，促进研发与市场需求对接。
4. **预期取得的知识产权**

   - 申请发明专利3项（涵盖安全芯片架构、抗量子加密算法等）。
   - 登记软件著作权5项（包括空域管理平台、安全验证工具链）。
   - 形成技术秘密2项（涉及TEE性能优化、攻击模拟算法）。
5. **技术壁垒与市场壁垒**：通过芯片级安全架构与国密算法融合，形成国产化替代能力，打破国外技术垄断；依托空域管理平台与政务系统合作，构建政府客户粘性，市场准入门槛提升30%。

### （四）考核指标

1. **技术指标**

   - 研发无人机安全管控芯片并完成流片：支持RSA2048、RSA4096、SHA256、SHA512、AES-CBC、AES-ECB等算法，支持国密算法SM1、SM2、SM3、SM4、SM7、SM9；加解密通过算法硬件加速实现高于1Gbps的数据吞吐带宽；对模块实施“不可绕过”的创新安全访问设计；安全存储机制设计防侵入和篡改，非法攻击数据擦除机制：硬件级电子围栏等。
   - 私有安全通信协议：具备抵御欺骗攻击和重放攻击等网络攻击的能力，安全性达到128bit级别以上，具备检测无人机控制参数组合错误的能力。
   - 无人机系统可信执行环境：基于可信执行环境进行安全加解密；TEE镜像占用固态存储空间小于3MB，运行空间小于12MB（不含TA）。TEE与REE之间的切换时间平均小于1ms，TEE启动速度小于0.8秒；提供防镜像攻击、恶意软件植入攻击、非法指令启动攻击及关键部件替换攻击等主要安全威胁的防护功能，防护覆盖率≥95%。
   - 完成无人机网络匿名无线通信安全防护、无人机飞行控制异常检测。构建高技术高成熟度的无人机系统协同可信安全防护技术验证平台。
2. **产学研用**

   - 基于西交网安研究院及西北大学的技术积累，依托文渊公司和唯识智能在集成、产品和产业化资源优势，完成无人机系统安全升级和技术验证平台，推出高安全等级样机并通过试验场测试。在陕西和浙江完成高安全应用场景企业接洽，完成2个实际应用案例并形成报告。
   - 建立产学研基地，共享芯片、仿真、无人机相关技术平台，在西交大、西北大学开设“无人机安全”相关课程，每年培养10名该领域优秀学生，并提供实习岗位；每年企业培养3名以上无人机安全复合型人才。
3. **知识产权成果**

   - 申请发明专利3项。
   - 登记软件著作权或版图共5项。
   - 形成技术秘密1项。

## 六、项目人员情况

| 项目组人员总数 | 25人 |
| -------------- | ---- |
| 高级职称       | 3人  |
| 中级职称       | 8人  |
| 初级职称       | 7人  |
| 其他           | 6人  |
| 博士           | 2人  |
| 硕士           | 3人  |
| 学士           | 19人 |
| 其他           | 0人  |

| 项目负责人 | 赵豫平           | 出生年月 | 1972-01-31                                                                                                                                                                                                                                                                                                                                           | 证件类型 | 身份证 | 证件号码 | *****                    | 性别     | 男         |
| ---------- | ---------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | ------ | -------- | ------------------------ | -------- | ---------- |
| 从事专业   | 计算机助理工程师 | 职称     | 总经理                                                                                                                                                                                                                                                                                                                                               | 学历     | 本科生 | 所在单位 | 西安文渊软件科技有限公司 | 项目分工 | 项目负责人 |
| 手机号码   | 13679105656      | 简介     | 1996年毕业于东北大学计算机系，曾任陕西诚泰科技讯息有限责任公司总经理，2016年创建西安文渊软件科技有限公司担任总经理。完成过深圳鹏城实验室FABS处理器后端设计、流片与封装测试采购项目、西交大创新港南区网络建设物联设备项目、综治视联网系统平台及网络技术服务项目、西交大信息化四网融合设备项目等。在系统集成、产品升级改造和项目组合管理方面有丰富经验 |          |        |          |                          |          |            |

| 主要研究人员 | 姓名   | 出生年月   | 证件类型 | 证件号码 | 性别 | 从事专业                             | 职称       | 职务             | 学历       | 所在单位                 | 项目分工                   | 手机号码    |
| ------------ | ------ | ---------- | -------- | -------- | ---- | ------------------------------------ | ---------- | ---------------- | ---------- | ------------------------ | -------------------------- | ----------- |
|              | 贺小伟 | 1977-02-10 | 身份证   | *****    | 男   | 信息与通信工程                       | 教授       | 信息办主任       | 博士研究生 | 西北大学                 | 算法研究员                 | 13891851598 |
|              | 王宾   | 1979-02-13 | 身份证   | *****    | 男   | 电子信息工程                         | 副教授     | 无               | 博士研究生 | 西北大学                 | 算法研究员                 | 13572802306 |
|              | 周兴龙 | 1996-06-28 | 身份证   | *****    | 男   | 计算机软件                           | 助理工程师 | 软件工程师       | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 15594798582 |
|              | 秦慧文 | 1982-07-13 | 身份证   | *****    | 男   | 软件测试                             | 未取得     | 助理             | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 15929485020 |
|              | 何超龙 | 1997-01-03 | 身份证   | *****    | 男   | 软件开发                             | 未取得     | 软件开发工程师   | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 16728715525 |
|              | 张勇   | 1988-08-19 | 身份证   | *****    | 男   | 软件研发                             | 未取得     | 研发经理         | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 17795607190 |
|              | 聂冬莉 | 1982-10-05 | 身份证   | *****    | 女   | 物联网                               | 技术员     | 员工             | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 13468846683 |
|              | 齐豪   | 1991-04-22 | 身份证   | *****    | 男   | 软件开发                             | 助理工程师 | 软件工程师       | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 15291484129 |
|              | 张特   | 1991-01-24 | 身份证   | *****    | 男   | 软件工程                             | 助理工程师 | 软件工程师       | 本科生     | 西安文渊软件科技有限公司 | 软件工程师                 | 15109211154 |
|              | 穆兰   | 1998-06-09 | 身份证   | *****    | 女   | 集成电路设计                         | 助理工程师 | 数字IC设计工程师 | 本科生     | 西交网络空间安全研究院   | 芯片工程师                 | 15022593665 |
|              | 张敏   | 1979-12-31 | 身份证   | *****    | 男   | 芯片系统设计                         | 工程师     | 集成电路负责人   | 本科生     | 西交网络空间安全研究院   | 芯片工程师                 | 13552383662 |
|              | 宫海天 | 1986-02-19 | 身份证   | *****    | 男   | 集成电路                             | 工程师     | 芯片工程师       | 本科生     | 西交网络空间安全研究院   | 芯片工程师                 | 15776745870 |
|              | 王乔   | 1990-04-17 | 身份证   | *****    | 男   | 系统工程                             | 助理工程师 | 数据安全工程师   | 本科生     | 西交网络空间安全研究院   | 信息安全工程师             | 18618126466 |
|              | 黄华成 | 1984-08-20 | 身份证   | *****    | 男   | 网络安全                             | 工程师     | 验证工程师       | 硕士研究生 | 西交网络空间安全研究院   | 嵌入式软件工程师、芯片研发 | 18600821784 |
|              | 曾晟   | 1997-05-10 | 身份证   | *****    | 男   | 电子信息                             | 助理工程师 | 固件工程师       | 本科生     | 西交网络空间安全研究院   | 嵌入式软件工程师           | 18850201058 |
|              | 林光煌 | 1985-02-09 | 身份证   | *****    | 男   | 电子信息工程                         | 助理工程师 | 芯片验证工程师   | 本科生     | 西交网络空间安全研究院   | 软件工程师                 | 18759970902 |
|              | 陈阳   | 2000-01-08 | 身份证   | *****    | 男   | 计算机                               | 工程师     | 固件工程师       | 本科生     | 西交网络空间安全研究院   | 固件工程师                 | 18150771159 |
|              | 王新君 | 1987-05-08 | 身份证   | *****    | 男   | 集成电路设计                         | 工程师     | 无               | 硕士研究生 | 西交网络空间安全研究院   | 芯片设计                   | 18810371901 |
|              | 李洋   | 1989-09-27 | 身份证   | *****    | 女   | 集成电路设计                         | 技术员     | 工程师           | 本科生     | 西交网络空间安全研究院   | 芯片设计                   | 13671396032 |
|              | 刘悦臻 | 1993-01-27 | 身份证   | *****    | 男   | 计算机软件                           | 高级工程师 | 无               | 本科生     | 西交网络空间安全研究院   | 安全算法专家               | 18030185159 |
|              | 唐凯   | 1985-09-06 | 身份证   | *****    | 男   | 人工智能、视频识别、边缘计算、大模型 | 讲师(高校) | 董事长           | 硕士研究生 | 浙江唯识智能科技有限公司 | 无人机系统负责人           | 18950016767 |
|              | 蒋周琼 | 1980-06-22 | 身份证   | *****    | 女   | 无人机                               | 未取得     | 总经理           | 本科生     | 浙江唯识智能科技有限公司 | 产品负责人                 | 13735311598 |
|              | 谢耀华 | 1982-08-14 | 身份证   | *****    | 男   | 软件开发                             | 工程师     | 研发人员         | 本科生     | 西交网络空间安全研究院   | 软件系统负责人             | 13400730390 |
|              | 斯贾琦 | 1998-05-17 | 身份证   | *****    | 男   | 无人机                               | 未取得     | 项目经理         | 本科生     | 浙江唯识智能科技有限公司 | 无人机系统工程师负责人     | 18705853957 |

## 七、项目进度计划

| 阶段    | 开始日期   | 结束日期   | 计划完成内容                                                                                                                                                                                                                                                                                                                                                                   |
| ------- | ---------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 第1阶段 | 2025-06-01 | 2025-06-30 | 1. 合作单位技术沟通会，信息共享，初步确定任务分工和合作。2. 完成项目技术方案和详细的项目实施计划书，完成项目启动会。3. 搭建研发环境，共享芯片开发、协议验证工具及仿真测试环境等平台。4. 成立产学研基地，采用"攻关课题+参与实战"模式校启联合开展工作。                                                                                                                          |
| 第2阶段 | 2025-07-01 | 2026-06-30 | 1. 基于已有进一步创新，研发用于无人机可信执行环境的安全管控芯片并完成样片流片。硬件加速1Gbps以上，安全访问设计，动态真随机发生器，硬件级电子围栏等功能。2. 研发安全可信嵌入式操作系统，支撑无人机系统设备安全多要素（访问/认证/时间/地点等多维度）和TEE系统轻量化指标。3. 轻量级安全私有协议。提供完整的加密通信、身份验证与完整性保护。具备检测无人机控制参数组合错误的能力。 |
| 第3阶段 | 2026-07-01 | 2026-12-31 | 1. 完成芯片级安全可信防护系统测试，完成无人机系统集成。硬件级可信执行环境达到预期的技术指标。2. 无人机管理平台安全升级具备安全防护技术验证功能，用于数据采集，监控和全系统安全运行，并进行安全应用验证。全链路测试验证端-边-云协同安全架构，优化数据可追溯性。3. 产学研基地完成10名研究生、博士生在安全芯片设计、无人机可信执行环境集成的项目培养。                            |
| 第4阶段 | 2027-01-01 | 2027-04-30 | 1. 完成安全无人机样品，将唯识自主控制算法、低空算法仓与芯片级可信防护系统结合，构建“端-边-云”三位一体安全架构，并在试验场完成测试验证，指标和安全功能满足项目考核要求。2. 推出高安全等级物流无人机和巡逻无人机（集成芯片级可信防护系统、安全私有协议、动态加密等），在不同应用领域接洽无人机应用场景和企业，完成2个实际应用案例形成报告。                                    |
| 第5阶段 | 2027-05-01 | 2027-05-30 | 1. 完成项目收尾和项目过程资产归档。2. 安全可信执行环境无人机系统未来规划。3. 知识产权成果：申请发明专利3项（涵盖安全芯片架构、抗量子加密算法等）；登记软件著作权5项（包括空域管理平台、安全验证工具链）；形成技术秘密2项（涉及TEE性能优化、攻击模拟算法）。                                                                                                                    |

## 八、项目经费预算表

### （一）项目经费表（金额单位：万元）

| 支出科目        | 项目总投资 | 专项资助经费 | 自筹经费 | 说明                                                               |
| --------------- | ---------- | ------------ | -------- | ------------------------------------------------------------------ |
| 一、直接费用    | 2000.00    | 750.00       | 1250.00  |                                                                    |
| 1、设备费       | 650.00     | 0.00         | 650.00   | 自筹集成电路设计、芯片测试、服务器、无人机系统集成测试等相关设备。 |
| 其中:设备购置费 | 0.00       | 0.00         | 0.00     |                                                                    |
| 2、业务费       | 530.00     | 530.00       | 0.00     |                                                                    |
| 3、劳务费       | 820.00     | 220.00       | 600.00   |                                                                    |
| 二、间接费用    | 30.00      | 30.00        | 0.00     |                                                                    |
| 1、管理费       | 10.00      | 10.00        | 0.00     |                                                                    |
| 2、绩效支出     | 20.00      | 20.00        | 0.00     |                                                                    |
| 合计            | 2030.00    | 780.00       | 1250.00  |                                                                    |

### （二）使用专项资助经费购买单价50万元以上设备明细表（金额单位：万元）

| 序号 | 设备名称 | 型号                        | 单价(万元/台件) | 数量(台件) | 金额(万元) | 用途(与课题研究任务的关系) |
| ---- | -------- | --------------------------- | --------------- | ---------- | ---------- | -------------------------- |
|      |          | (单位财务专用盖章/财务公章) |                 |            |            |                            |

### 五、项目进度计划（说明项目进度，包括实施方案、实施地点等内容）

#### 第1阶段（2025-06-01至2025-06-30）：项目启动与环境搭建

- **实施方案**：
  1. 组织西安文渊软件科技有限公司、西交网络空间安全研究院、西北大学、浙江唯识智能科技有限公司等合作单位召开技术沟通会，明确各单位在芯片研发、协议验证、系统集成等环节的任务分工，建立信息共享机制。
  2. 结合指南方向与技术目标，制定详细的项目技术方案和实施计划书，涵盖研发流程、质量控制标准及风险应对策略，并召开项目启动会同步至各参与方。
  3. 整合各方资源搭建研发环境，包括芯片开发平台、协议验证工具、无人机仿真测试环境等，实现硬件开发与软件测试环境的共享。
  4. 在西安的西交网络空间安全研究院及西北大学设立产学研基地，采用“攻关课题+参与实战”模式，推动高校科研力量与企业技术需求对接。
- **实施地点**：主要在西安市，涉及西安文渊软件科技有限公司办公地、西交网络空间安全研究院及西北大学实验室。

#### 第2阶段（2025-07-01至2026-06-30）：核心技术研发

- **实施方案**：
  1. 西交网络空间安全研究院主导研发无人机可信执行环境的安全管控芯片，重点突破硬件加速（数据吞吐≥1Gbps）、动态真随机发生器、硬件级电子围栏等功能，完成样片流片。
  2. 同步研发安全可信嵌入式操作系统，支撑无人机设备的多维度安全管控（访问、认证、时间、地点等），实现TEE系统轻量化（镜像存储≤3MB，运行空间≤12MB）。
  3. 西北大学负责设计轻量级安全私有协议，具备加密通信、身份验证及完整性保护能力，可检测无人机控制参数组合错误，抵御网络攻击。
- **实施地点**：西安市（芯片研发与系统开发）、诸暨市（浙江唯识智能科技有限公司参与部分测试）。

#### 第3阶段（2026-07-01至2026-12-31）：系统集成与测试

- **实施方案**：
  1. 西安文渊软件科技有限公司牵头完成芯片级安全可信防护系统的性能测试，与浙江唯识智能科技有限公司合作实现无人机系统集成，确保硬件级可信执行环境达标。
  2. 升级无人机管理平台，新增安全防护技术验证功能，用于数据采集、监控及全系统安全运行，验证“端-边-云”协同安全架构的有效性，优化数据可追溯性。
  3. 产学研基地通过项目实践培养10名研究生、博士生，重点参与安全芯片设计与无人机可信执行环境集成工作。
- **实施地点**：西安市（系统集成）、浙江省诸暨市（无人机平台测试）。

#### 第4阶段（2027-01-01至2027-04-30）：样品研发与应用验证

- **实施方案**：
  1. 浙江唯识智能科技有限公司整合自主控制算法、低空算法仓与芯片级可信防护系统，构建“端-边-云”三位一体安全架构，完成安全无人机样品研发并通过试验场测试。
  2. 推出高安全等级物流无人机（WS30）和智能安防巡逻无人机（JDYAir Pro），在物流、应急、安防等领域接洽应用场景，完成2个实际应用案例并形成报告。
- **实施地点**：西安市（样品优化）、浙江省（应用场景落地）。

#### 第5阶段（2027-05-01至2027-05-30）：项目收尾与成果固化

- **实施方案**：
  1. 完成项目资料归档，包括技术文档、测试报告、专利申请材料等，总结项目实施经验并规划未来技术迭代方向。
  2. 落实知识产权成果：申请发明专利3项（安全芯片架构、抗量子加密算法等）、登记软件著作权5项（空域管理平台、安全验证工具链等）、形成技术秘密2项（TEE性能优化、攻击模拟算法）。
- **实施地点**：西安市（项目总结与成果整理）。

### （三）专项经费基本说明（金额单位：万元）

| 支出科目                          | 数量    | 金额(万元)                  | 说明(对测算金额进行具体说明)                                             |
| --------------------------------- | ------- | --------------------------- | ------------------------------------------------------------------------ |
| 业务费                            |         | 530.00                      | 无特殊说明                                                               |
| 其中:材料费                       | 3000.00 | 200.00                      | 芯片、模组、无人机系统安全升级开发测试用电路板、电子元器件、样机等耗材。 |
| 测试化验加工费                    | 1.00    | 312.00                      | 无人机专用安全管控芯片(40nm)样品流片、封装等生产委外费用。               |
| 燃料动力费                        | 0.00    | 0.00                        |                                                                          |
| 差旅费                            | 15 人次 | 10.00                       | 技术交流，产品测试等。                                                   |
| 会议费                            | 5.00    | 3.00                        | 会议费用                                                                 |
| 国际合作与交流费                  | 0.00    | 0.00                        |                                                                          |
| 出版/文献/信息传播/知识产权事务费 | 5.00    | 5.00                        | 无特殊说明                                                               |
| 其他费用                          | 0.00    | 0.00                        |                                                                          |
| 劳务费                            | /       | 220.00                      | 无特殊说明                                                               |
| 其中:专家咨询费                   | 2 人    | 20.00                       | 专家咨询费                                                               |
| 聘用人员劳务费                    | 20 人   | 200.00                      | 600万自筹                                                                |
| 其他劳务费                        | 0 人    | 0.00                        |                                                                          |
|                                   |         | (单位财务专用盖章/财务公章) |                                                                          |

15/99

### 七、项目承担单位近三年财务状况

金额单位(万元)
注:计算依据简要说明可另附页。

| 财务指标      | 年度     |          |          |
| ------------- | -------- | -------- | -------- |
|               | 2022 年  | 2023 年  | 2024 年  |
| 主营业务收入  | 772.05   | 972.11   | 2,172.43 |
| 主营业务支出  | 0        | 0        | 0        |
| 其中:研发投入 | 0        | 0        | 0        |
| 利润总额      | 38.73    | 90.67    | 259.29   |
| 资产总额      | 1,196.78 | 1,718.78 | 2,161.21 |
| 负债总额      | 718.17   | 1,049.01 | 1,100.90 |
| 资产负债率    | 60.00    | 61.03    | 50.93    |
| 利润率        | 15.79    | 12.25    | 16.13    |
| 银行贷款      | 0        | 0        | 0        |

16/99

### 八、项目合作单位经费支出预算表

| 序号 | 单位名称               | 统一社会信用代码   | 注册类型         | 任务分工                                                                                                                                                                                                                                                                      | 研究任务负责人 | 合计 | 专项经费 |          | 自筹经费 |
| ---- | ---------------------- | ------------------ | ---------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------- | ---- | -------- | -------- | -------- |
|      |                        |                    |                  |                                                                                                                                                                                                                                                                               |                |      | 直接费用 | 间接费用 |          |
| 1    | 西交网络空间安全研究院 | 12330681MB1L38368J | 自收自支事业单位 | 研发自主可控的无人机芯片级安全防护体系，包括可信安全执行环境的安全管控芯片、可信嵌入式操作系统的开发，现具有监测-响应-预测-防御闭环的全域动态安全防护能力，可有效防范镜像攻击、恶意软件植入攻击、非法指令启动攻击及关键部件替换攻击等主要安全威胁，实现核心技术的国产化突破。 | 张敏           | 810  | 400      | 10       | 400      |

17/99

| 2 | 浙江唯识智能科技有限公司 | 91330681MACTR8PT0N | 有限责任公司               | 可信执行环境与无人机系统融合集成，达成项目相关安全测试要求。推动技术成果向高可靠无人机产品的转化，开拓国防与民用市场。                                                   | 唐凯   | 450    | 150 | 0  | 300 |
| - | ------------------------ | ------------------ | -------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------ | ------ | --- | -- | --- |
| 3 | 西北大学                 | 126100004352012743 | 全额事业单位(如高等院校等) | 安全私有协议开发和在无人机系统中的测试，支持形式化方法建模与验证的安全通信协议，可抵御欺骗攻击、重放攻击等典型网络攻击，确保数据传输的完整性与机密性，达到国际领先水平。 | 贺小伟 | 110.00 | 100 | 10 | 0   |
|   |                          |                    |                            |                                                                                                                                                                          | 累计   | 1370   | 650 | 20 | 700 |

18/99

### 七、项目可行性研究报告

#### 提纲

##### （一）项目立项的必要性

1. **国内外技术发展趋势**
   中国无人机产业在技术创新与场景应用领域已形成全球领先优势，在可信执行环境(TEE)与数据信息安全方面，也取得了显著进展，处于蓬勃发展阶段。在系统安全方面，大疆的行业级无人机通过硬件级可信执行环境(TEE)实现飞控指令与敏感数据的物理隔离，满足《无人驾驶航空器飞行管理暂行条例》对数据主权的监管要求。在动态身份认证方面，基于北斗导航系统的多因子认证技术已被应用于农业无人机领域，极飞科技P系列机型通过生物特征(如指纹)与北斗短报文双重验证，降低非法劫持风险。在数据安全与隐私保护方面，国内民用无人机普遍采用轻量化隐私计算框架，如华为云开发的边缘节点算法可在本地完成航拍影像脱敏处理，避免敏感地理信息泄露。

在无人机的安全防护技术领域，目前国内产业短板问题主要在芯片等核心元器件、操作系统、高精度传感器、量子加密模块、协议漏洞等。尤其硬件方面自主可控能力不足，在抗攻击核心技术、动态抗干扰能力、加密等核心技术成熟度也存在不足，算法标准化等方面仍需提升，与国际领先的形式化验证通信协议和128bit以上加密标准有一定差距，现有主流技术有通信链路加密(如跳频、扩频技术)、飞控系统安全加固(防止恶意代码注入)、数据防篡改等。可信执行环境(TEE)方面，国际目前主流发展技术领域在多源传感器融合定位系统与多传感器融合验证、硬件级安全模块、动态可信验证，数据信息安全技术在抗干扰通信、端到端加密及隐私保护、主动防御。典型产品包括道通智能Autel Alpha（Autonomy自主飞行技术、双红外热成像加密联动、抗量子加密传输）、Maxar Raptor系统（3D地图匹配定位、多传感器冗余校验、抗GPS欺骗）、彩虹5无人机（端云协同加密）、翼龙3无人机（全自主飞行控制、区块链数据存证）等。国际方面最新产品：美国Auterion推出了Skynode S芯片，是一款可内置到无人机中的控制芯片，用于军用无人机操作的新型计算机和飞行控制系统，具有较高的安全特性，可自主引导神风无人机前往乌克兰的目标，在部署期间，绕过电子对抗的成功率非常高。主要先进技术包括计算机视觉算法和硬件设计，可在GPS信号失效或射频通信被干扰的环境中实现自主导航与目标跟踪；识别被伪装、部分遮挡或处于移动状态的优异算法，在动态环境表现可靠；采用低成本、芯片化和一体化设计，确保硬件可信度与集成安全性。在可信执行环境方面，英特尔与美国国防部DARPA-SAHARA计划研发并生产用于美国国防系统的定制安全微芯片已经逐渐应用于无人机。无人机可信系统安全防护与信息安全技术正面临智能化升级(AI威胁识别)、技术融合(加密+边缘计算)和适配(数据主权与标准互认)三大趋势。技术攻关需聚焦硬件自主化(芯片、量子模块)、协议安全性(动态跳频、轻量化隐私计算)与威胁预测能力(建模，AI驱动的动态可信验证体系)等。

2. **项目技术攻关的必要性**
   低空经济作为战略性新兴产业，已成为我国经济增长的新引擎。在低空经济相关产业中，无人机产业优势显著，在个人影视航拍等领域广泛的应用基础上，应急救援、智慧农业等新应用场景不断被开发和规模化应用。然而，随着无人机数量的激增，安全风险成为制约其发展的核心问题。俄乌战争、也门冲突中的实际案例表明，通过网络攻击入侵信息系统，从而恶意操控无人机实施恐怖袭击，会严重威胁关键基础设施和敏感区域安全。因此，急需推动和发展无人机安全的核心技术研发与应用，构建包括安全管控芯片、安全通信协议、可信执行环境、综合管控系统等在内的一体化安全防护体系，从而有效防范针对无人机的非法入侵、窃密或攻击等行为，为低空经济规模化发展提供安全基石。

目前我国无人机安全领域与国际先进水平相比还存在明显差距，主要体现在：

- 部分核心部件依赖进口：无人机用高端安全芯片、高精度传感器等核心部件仍依赖进口，自主可控能力不足，尤其在抗攻击核心技术、动态抗干扰能力等方面存在短板；
- 抗干扰与加密等核心技术成熟度不足：在动态抗干扰算法、安全通信协议标准化等方面仍需提升，与国际领先的形式化验证通信协议和128bit以上加密标准有明显差距；
- 安全可信的综合防护体系尚未建立：与国际先进水平相比，缺乏芯片级防护、执行环境可信、全域闭环动态防护等能力。

针对无人机面临的主要安全威胁，以网络安全防护为切入点，构建适配无人机应用场景的包括安全管控芯片、安全通信协议、可信执行环境、综合管控系统等在内的一体化安全防护体系，形成芯片级防护、执行环境可信、全域闭环动态防护等核心能力，切实保护无人机免于飞控系统劫持、数据链路入侵、虚假身份欺骗、空域规避失效等安全威胁，实现从底层硬件到上层应用的无人机全域闭环动态防护，最终达成无人机物理安全防护能力。基于取得的核心技术成果，研发无人机安全防护系列化软硬件产品，并开展规模化应用推广，实现经济价值与社会效益双示范。

3. **项目的市场需求分析**
   据Frost & Sullivan数据显示，2023年全球民用无人机市场规模已达320亿美元，预计到2028年将突破800亿美元，年复合增长率超15%。中国作为全球最大的无人机生产国和消费国，2025年民用无人机市场规模预计达1691亿元，注册无人机数量突破198万架，同比增长50%(数据来源:中国航空运输协会)。安全管控产品作为配套刚需，预计占据10%-15%市场份额，对应超250亿元市场空间。然而，无人机安全问题日益严峻：黑飞事件、数据劫持、硬件篡改等威胁频发，仅2023年国内因无人机失控引发的公共安全事件就超过200起，直接经济损失达3亿元。低空经济作为综合性经济形态，涵盖了航空器制造、通航运营、低空旅游、应急救援、物流配送等多个环节，展现出巨大的市场潜力。提高无人机安全性可以减少黑飞事件和违法侵入，提升公共安全保障能力。

政策层面，中国政府不断加大对低空经济的支持力度，多项政策文件明确提出了要积极发展通用航空和低空经济。中国《无人驾驶航空器飞行管理暂行条例》明确无人机需购买责任险，强调飞行安全与合规运营，要求无人机配备电子围栏、远程监控等安全功能，这直接推动企业对可信执行环境(如飞行数据加密、身份认证)及安全管控产品(如反制系统、空域管理平台)的需求，以满足监管要求并降低事故风险。截至2025年，中国已设立17个民用无人驾驶航空试验区，覆盖城市、物流等场景。试验区对无人机飞行安全、通信加密及动态监控提出更高要求，催生低空安全管控系统的规模化应用。欧盟SORA 2.0框架及美国FAA法案强化跨境飞行的安全认证与数据合规标准，也催生全球化安全解决方案需求。

##### （二）项目主要研究内容及创新点

1. **主要目标及考核指标**
   研究建立自主可控的芯片级安全防护体系应用于无人机可信执行环境安全防护、匿名无线通信安全防护、无人机飞行控制异常检测、抵御欺骗攻击和重放攻击等网络攻击。

- **核心技术突破**:

  - 自主可控的无人机芯片级安全防护体系：研制具备完全自主知识产权的安全管控芯片及相关系统，实现具有监测-响应-预测-防御闭环的全域动态安全防护能力，可有效防范镜像攻击、恶意软件植入攻击、非法指令启动攻击及关键部件替换攻击等主要安全威胁，实现核心技术的国产化突破。
  - 高安全级加密与认证能力：基于可信执行环境的安全加解密模块兼容国际通用算法(如AES、RSA)与国密算法(SM2/SM3/SM4)，加解密性能满足实际应用场景需求，并通过强化身份认证与动态访问控制机制，实现无人机系统间的可信互认，杜绝非法接入与身份伪造风险。
  - 形式化验证的安全通信协议：支持形式化方法建模与验证的安全通信协议，可抵御欺骗攻击、重放攻击等典型网络攻击，确保数据传输的完整性与机密性，达到国际领先水平。
- **考核指标**:

  - 安全管控芯片：通过国密加解密算法硬件加速，实现高于1Gbps的数据吞吐带宽；
  - 安全访问设计：对CPU、总线、中断、内存等模块实施“不可绕过”的安全访问设计，并建立安全存储机制以防止侵入和篡改，引入非法攻击缓存安全区数据擦除机制，并支持电子围栏功能；
  - 安全通信协议：具备抵御欺骗攻击和重放攻击等网络攻击的能力，安全性达到128bit级别以上，具备检测无人机控制参数组合错误的能力；
  - 可信执行环境：基于可信执行环境进行安全加解密，支持RSA2048、RSA4096、SHA256、SHA512、AES-CBC、AES-ECB等算法，并扩展至国密算法SM1、SM2、SM3、SM4、SM7、SM9；
  - TEE性能优化：TEE镜像占用固态存储空间小于3MB，运行空间小于12MB(不含TA)。TEE与REE之间的切换时间平均小于1ms，TEE启动速度小于0.8秒；
  - 综合安全防护：提供防镜像攻击、恶意软件植入攻击、非法指令启动攻击及关键部件替换攻击等主要安全威胁的防护功能。

2. **研究与开发内容**
   基于零信任理念，研究建立自主可控的芯片级安全防护体系，从硬件层面保护数据资源受到的非授权访问、篡改或盗取，实现数据和通道两方面的安全及管控。应用于无人机可信执行环境安全防护、匿名无线通信安全防护、无人机飞行控制异常检测、抵御欺骗攻击和重放攻击等网络攻击。

研发用于无人机可信执行环境的安全管控芯片。具备高性能数据处理、高密文数据吞吐、独有安全存储机制、CPU/总线/中断/内存安全访问设计，支持RSA2048、RSA4096，SHA256，SHA512，AES-CBC，AES-ECB等加解密算法硬件加速，支持SM2，SM3，SM4等国密算法硬件加速，加解密性能不低于1000Mbps。

研发用于无人机可信执行环境的嵌入式操作系统和安全私有加密协议。实现安全世界与非安全世界的高效、低损耗切换，确保上下文切换时关键数据不被泄露；设计时间、地点等多因子认证的动态融合机制，在保证安全性的同时降低认证延迟；构建安全存储区的硬件隔离与访问控制策略，防止侧信道攻击和权限越界访问；优化安全监控机制，实时检测异常行为并快速响应。保证传输过程中的协议安全。此外，还需解决多安全域间的通信效率问题，避免因安全隔离导致的系统性能瓶颈。系统支持芯片级嵌入式可信执行环境安全防护、基于固有安全技术的轻量级可执行安全管控机制，可支撑实现无人机系统设备安全多要素和无人机系统网络空间安全，TEE系统轻量化，TEE镜像占用固态存储空间小于3MB，运行空间小于12MB(不含TA)，TEE和REE切换平均时间小于1ms，TEE启动速度小于0.8秒。

对现有无人机系统进行安全升级，融合以上硬件级可信安全执行环境。并建立无人机管控平台为高成熟度高技术的安全防护技术验证平台。

3. **项目的技术关键**

- **关键技术一:加解密算法硬件加速**
  安全管控芯片内置国密加解密算法硬件加速器、FPSM模块和ASDMA模块。创新点是可以在较低的CPU占用率下实现加速算法数据吞吐量和加解密运算性能。关键技术是可实现加速算法数据吞吐量和加解密运算性能的FPSM模块和ASDMA模块实现方法。

技术路线简述: FPSM为现场可编程状态机模块，其拥有指令ram和一路AHB master控制器。FPSM可以模仿CPU的AHB master端口动作，可以作为协处理器模拟CPU对于UAC ori模块的访问，其功能包含控制加解密数据的数量；写入KEY；写入数据；读出数据；读取状态寄存器；寄存器清零等等动作。ASDMA模块可以实现Mem(连续地址)对外设(固定地址)读出和写入。ASDMA模块负责从Mem中指定地址读取待加密数据并送入FPSM模块的RX FIFO中，并在加密操作后把FPSM模块TX FIFO中的加密数据送入Mem中(连续地址)。FPSM模块和ASDMA模块只在配置时需要CPU进行访问，工作时不需要CPU介入，因此可以大幅降低CPU占用率。

- **关键技术二:高限制写存贮区保护机制**
  实现多级安全存储器和分级保护机制，建立OTP永久不可更改存贮空间和高限制性写存贮空间，OTP存储区存储芯片ID及预留校验码等永久不可更改的信息，高限制性写存贮空间用于贮存各项安全机制实现中用到的敏感数据信息、白名单等。技术创新点是高限制写存贮区拥有开放的读权限，但是只能通过特定专用授权的接口板卡才能对该区域写入修改。

技术路线:
关键机制包括身份认证、数据加密和数据完整性验证，此三项是保证数据存储安全的关键技术。安全存储系统以软件或者硬件方式实现这些关键技术，对数据流进行控制和变换，从而达到保护敏感信息得目的。

身份认证:安全存储中常用得身份认证技术有口令认证，生物特征识别和硬件密钥三类。

数据加密技术:数据加密技术时保证数据存储安全做常用和做有效的技术。

数据完整性技术验证:安全存储系统中，必须对动态或者静态存储得数据提供完整性验证，以防止修改密文或者回访攻击。

- **关键技术三:动态真随机发生器机制**
  安全管控芯片内置动态真随机发生器，通过硬件级安全设计、高效能输出和低资源占用，成为芯片安全管控系统的核心组件。内置动态真随机发生器由专用电路实现，具有低延迟和高吞吐量的特性，可快速生成高质量随机数，满足实时加密需求。

创新设计及技术路线:
安全管控芯片内置动态真随机发生器，通过硬件级安全设计、高效能输出和低资源占用，成为芯片安全管控系统的核心组件。内置动态真随机发生器由专用电路实现，具有低延迟和高吞吐量的特性，可快速生成高质量随机数，满足实时加密需求。随机数产生过程无需CPU参与，降低主处理器负载，无需外接硬件元器件，降低PCB设计复杂度及BOM成本。它不仅解决了软件PRNG的熵不足和可预测性问题，还通过物理隔离显著提升了抗攻击能力。

动态真随机发生器具有以下技术特性:内置4路独立随机振荡源；多振荡器组合冗余熵源和纠错机制；内置数字线性反馈移位随机数处理器；支持软件定义随机掩码值种子；支持频数自检、全0或全1自检和环路自检。

- **关键技术四:芯片独立时间防护机制**
  通过独立电源域和独立复位域技术实现系统时间的独立性及防护。创新点是芯片内部计时模块的独立电源域和独立复位域技术实现方法，在上电/下电和复位阶段保障芯片内部计时独立，以及芯片内部计时模块RTC的操作权限控制方法。

技术路线:独立时间RTC的安全防护设计:1,独立电源域，电池供电，防拆机无法拆卸电池。2,RTC配置接口可以软件锁定，软件无法解除。上电后，Bootloader配置RTC并锁定，时间无法篡改。3,高限制存储区重载接口，通过外接板卡实现。此外接板卡可以在重载高限制存储区的时候，选择解锁RTC模块的配置锁定模式，从而实现热状态重新配置系统时间。4,为RTC模块设计独立复位域保障芯片内部计时的独立性。以上设计避免主系统在冷启动过程中对RTC寄存器内容的误改写，保证了时间无法被篡改，保证时间的可靠性。专用设计的RTC控制主电源下电/上电的电源连接方式，当主系统没有待处理任务时，其通过内部总线，配置RTC寄存器，包括电源唤醒时间，RTC计时使能，主电源下电使能等。当RTC计时时间到，自动控制芯片的电源控制管脚输出高电平，使片外的电源管理芯片(PMIC)重新给芯片的主电源供电。

- **关键技术五:可信安全管控操作系统**
  自研嵌入式可信安全操作系统，实现安全与自主可控。在CPU上同时隔离运行非安全核(任务核)和安全核(管控核)。普通世界中的代码在非安全核上运行，只能访问普通世界的硬件资源，运行通用操作系统，主要负责普通应用任务的执行；安全世界中的代码在安全核上运行，能够访问所有硬件资源，运行安全操作系统，主要负责管控任务的运行。

可信安全操作系统整体框架:

```
监控模式
普通世界      安全世界
通用操作系统  全内核
普通应用       安全应用
APP  APP      APP  APP
```

系统层面(时间，地点多维度实时管控):
安全芯片内置的RTC以及定位信息保障无人机的实时的安全可控，确保其飞行时间的合法以及飞行的位置的合法。可以对其进行实时安全管控，进一步保障其安全性和可控性。

系统层面(安全可信存储区域):
芯片内含有特殊的安全可信存储区，高限制可信的存储区可以有效地保护回传图像的安全性。避免数据的泄露，保护一些关键的影像数据。必须采用专用授权的接口才能访问到这些存储区数据。

系统层面(安全私有传输协议):
设计基于Diffie-Hellman密钥交换的轻量级传输层安全私有加密协议。它支持多种加密算法，并独特支持国密算法，提供完整的加密通信、身份验证与完整性保护。

特点:

- 集成SM2,SM3,SM4国密算法
- 模块化的协议框架
- 可构建定制化的安全通信方案
- 双向DH密钥交换机制
- 支持多种不同类型DH函数
- 多种不同类型的加密算法哈希函数

```
Private key (da)        Private key (da)
Public key: Qu=daxG     Public key: Q=dgxG
        QB
Shared key: Share=dxQa  Shared key: Share =dg x QuA
Shared key:             Shared key:
Share =da xdgxG         Share=dg x daxG
```

技术难点:
可信安全管控操作系统的核心难点在于:如何实现安全世界(TrustZone)与非安全世界的高效、低损耗切换，确保上下文切换时关键数据不被泄露；如何设计时间、地点等多因子认证的动态融合机制，在保证安全性的同时降低认证延迟；如何构建安全存储区的硬件隔离与访问控制策略，防止侧信道攻击和权限越界访问；以及如何优化安全监控机制，实时检测异常行为并快速响应。如何保证传输过程中的协议安全。此外，还需解决多安全域间的通信效率问题，避免因安全隔离导致的系统性能瓶颈。

创新性:
该方案的创新性体现在:1)提出基于动态权重的多因子认证模型，结合时间、地理位置、设备指纹等动态因子，实现自适应安全等级调整；2)设计轻量级安全世界切换协议，通过硬件加速(如ARM TrustZone扩展指令集)减少切换开销；3)引入安全存储区的分层加密机制，结合国密算法(如SM4)实现数据分片加密存储，支持细粒度访问控制；4)创新性地将可信执行环境(TEE)与区块链技术结合，确保操作日志的不可篡改性和可追溯性。5)基于TLS的协议，进行轻量型的专用协议定制化，保证通信协议的安全性。

➢ **行业共性技术**:
该方案依赖的行业共性技术包括:1)ARM TrustZone或RISC-V PMP等硬件隔离技术，提供安全世界的基础隔离能力；2)国际通用的多因子认证标准(如FIDO2)或国密算法(如SM2/SM3)，用于身份验证与数据完整性保护；3)安全存储技术(如eMMC RPMB分区)或加密文件系统(如EncFS)，实现数据的硬件级保护；4)可信计算技术(如TPM 2.0)，用于建立硬件信任根。这些技术已在金融、政务等领域形成成熟应用。

➢ **关键技术**:
关键技术涵盖:1)安全上下文快速切换技术，通过寄存器状态压缩和预加载优化切换性能；2)动态多因子认证引擎，支持基于风险评估的认证强度动态调整；3)安全存储区的密钥派生与隔离技术，采用SM4现分区分级加密；4)实时安全监控技术，基于行为分析和异常检测)识别攻击行为；5)安全私有协议防护技术，在信息交互层面进行协议私有定制，保证数据交互安全。

技术路线:建议分阶段推进:1)基础层:完成TrustZone或类似硬件的适配，实现安全世界与非安全世界的隔离与切换；2)核心层:集成多因子认证引擎和安全存储模块，支持国密算法与动态策略；3)增强层:引入实时监控与可信链技术，覆盖从启动到运行时的全生命周期防护；4)优化层:通过硬件加速(如密码算法专用指令集)和安全私有协议提升性能；5)验证层:通过私有协议或国密安全认证，确保方案符合高等级安全标准。该路线遵循"硬件隔离-动态防护-全栈可信"的递进逻辑，适配信创产业对自主可控的需求。

- **创新点六:基于芯片国密算法的安全启动**
  基于国密算法，更有利于国产替代，更进一步确保了功能的安全性。

利用国密算法SM3，SM4加解密以及SM2的签名验签，有效地保证了无人机固件的启动的安全性。安全启动包含了加解密以及签名验签两部分。加解密保证了固件的安全性，不易被反汇编。签名验签安全启动保证了固件的合法性，确保不运行非法的固件。

➢ **技术难点**:
在基于国密算法的芯片安全启动方案中，主要技术难点包括如何高效实现国密算法的硬件集成，确保SM2/SM3等算法在资源受限的物联网芯片上的性能表现；如何设计安全的密钥管理机制，防止启动过程中的密钥泄露；以及如何构建完整的信任链，从芯片底层到应用层实现无缝的安全验证。特别是在固件验签环节，需要解决大容量固件的分段验签效率问题，同时抵御侧信道攻击等物理层威胁。

创新点:
该方案的核心创新在于将SM2数字签名算法与SM3哈希算法深度整合到安全启动流程中，构建国产密码体系的全栈保护。具体包括:采用SM2-1椭圆曲线数字签名算法实现固件签名验证，利用SM3替代传统SHA-256进行完整性校验；设计基于SM4的分段加密验签机制，支持大容量固件的流式验证；实现一芯一密的个性化安全启动。

➢ **行业共性技术**:
该方案涉及的行业共性技术包括:符合GM/T0002标准的SM4分组密码实现技术，支持CBC等加密模式；基于GM/T0003的SM2椭圆曲线算法应用技术，满足数字签名和密钥交换需求；遵循GM/T0004的SM3杂凑算法实现技术，提供与SHA-256相当的安全性。这些技术已在金融、政务等领域形成标准化应用方案，具备良好的产业基础。

➢ **关键技术**:
实现该方案的关键技术包含:SM2公钥加密算法在芯片级的安全实现技术，确保签名验证过程的安全高效；SM3消息扩展和压缩函数的硬件优化技术，提升哈希计算性能；安全启动信任链构建技术，实现从ROM Bootloader到应用层的逐级验证；抗物理攻击的安全存储技术，保护根密钥和关键参数。其中，SM2与SM3的协同验签技术是突破传统国际算法依赖的核心。

➢ **技术路线**:
采用以下技术路线:第一阶段完成SM2/SM3算法的硬件IP核设计，通过GM/T标准认证；第二阶段开发安全启动协议栈，实现固件签名、验签和版本控制的全流程管理；第三阶段整合SM4加密，构建多层次防护体系；最终阶段进行侧信道防护加固，形成完整的国密安全启动解决方案。该路线既遵循"算法国产化-协议标准化-系统集成化"的演进路径，又符合信创产业对密码技术自主可控的要求。

##### （三）技术、经济效益

1. **技术经济效益分析(含经济效益、社会效益)**
   2025年中国民用无人机市场规模预计达1691亿元，其中安全管控产品作为刚需占比超15%(约250亿元)。政策方面《无人驾驶航空器飞行管理暂行条例》催生可信执行环境(TEE)与空域管理平台的刚性需求。全国17个民用无人驾驶航空试验区建设，进一步推动低空安全系统规模化应用。项目构建"技术攻关-产品开发-产业落地"全链条创新体系，技术赋能产品创新，拓展行业应用场景。唯识智能以"低空数字系统+AIOT"为核心，推动安全技术深度融入无人机产品体系，打造差异化竞争力。

研发的无人机安全管控芯片与系统相关产品，可依靠项目成果安全专利提升竞标竞争力，实现千万级的市场销售收入。将自主控制算法、低空算法仓与安全协议结合，构建"端-边-云"三位一体安全架构，推出高安全等级物流无人机WS30(动态加密、抗干扰通信)、智能安防巡逻无人机JDYAir Pro(集成TEE与AI决策模块)，覆盖多个场景。提高高安全性无人机在医疗救援、消防等高风险场景的应用渗透率，减少黑飞事件和违法侵入，提升公共安全保障能力。建立"硬件销售-系统订阅-数据增值"多元收入模型，客单价提升至500万-1000万元(城市级合作)。通过政务无人机、网格化自动机场、数据中台等产品，覆盖农业、交通、物流等领域。

2. **推广应用前景分析(含产业化可行性)**

- 行业应用覆盖:重点推进农/林植保、物流配送、安防巡逻、电力巡检、环境监测、地质勘探、应急救援等领域的无人机规模化安全应用，以唯识智能科技公司为基础，扩展到各无人机应用企业，计划三年内完成30,000台无人机安全技术融合，形成行业示范效应。
- 市场渗透目标:依托核心技术优势，五年内实现无人机市场安全技术应用覆盖率超20%，推动行业从单一功能设备向智能化、可信化生态升级，助力低空经济高质量发展。

3. **风险评估**

- 技术突破不确定性风险:TEE(可信执行环境)轻量化、加解密算法硬件加速硬件兼容性与性能瓶颈、抗侧信道攻击能力不足。
- 技术快速迭代压力:华为计划2025年发布5nm制程无人机专用AI芯片，算力达20TOPS，是否匹配最新安全功能未知。
- 专利壁垒与国际竞争:头部企业技术垄断如大疆在无人机导航避障领域布局2000+项专利，需规避侵权风险并加速自有专利布局。欧盟SORA 3.0标准要求无人机通信协议通过EAL5+级认证，但国内现有技术尚未完全满足该标准，可能限制全球化市场准入。
- 技术应用与适配风险:物流无人机需在复杂城市低空环境(如强电磁干扰、密集建筑群)中保持稳定通信，现有动态加密技术的丢包率仍高达3%，可能引发指令延迟或失控。
- 安防无人机的AI自主决策算法依赖海量数据训练，试验区可能不足。

##### （四）现有工作基础

1. **现有技术和工作进展**
   西交网络空间安全研究院由中国国科学院院士管晓宏担任院长和首席科学家，面向网络与数据安全领域科技发展前沿，研发网络安全自主可控专用芯片、软硬结合内外网安全管控系统核心技术，提供行业安全管控整体解决方案。建设的智能物联网络与数据安全重点实验室获国家大奖8项，其中包括国家自然科学二等奖。现有产品赛安安全管控芯片及配套软硬件系统，具备硬件级安全、嵌入式操作系统安全和管控平台安全等方面的技术经验和积累，专利15项。在安全计算机、信息发布系统、工业物联网等领域有着广泛应用。

浙江唯识智能科技有限公司专注于"低空数字系统+AIOT"产品应用，以人工智能技术为核心，集成自主研发边缘计算设备，构建基于数字视网膜技术的低空算法仓平台，提供全空间GIS框架下的基于"端边云"创新架构的SaaS低空数字综合应用系统。在无人机自动控制、AI视频识别、数据智能分析领域有领先的技术手段，包括基于无人系统协同控制的无人机自主控制算法、基于数字视网膜技术的低空算法仓、基于数字视网膜技术的智慧低空算法仓、基于特征提取、语义分割的智能识别算法、基于低空数据驱动的无人机指挥调度决策模型。

西安文渊科技有限公司具备军密二级资质以及ISO质量体系认证证书，在军工项目及系统工程项目方面具备良好能力，在系统集成、平台搭建、技术融合方面有着丰富经验。其中基于国产信创体系的智慧信息化产品得到了广泛应用，如物联信息化管理平台、云边端一体化安全管控平台等公司与多所高校及科研院所建立了产学研的合作关系，其中与西交大在物联安全管控及物联场景实现技术创新领域紧密合作，自主研发的基于物联安全管控系统入选陕西省军民融合产品名目。

西北大学是综合性全国重点大学，有24个博士学位授权点、57个硕士学位授权点，24个博士后流动站。西北大学搭建了全球首个基于高斯映射的CV-QSDC实验平台，验证了连续变量量子通信协议的有效性，并提出了信道参数估计与信号分级处理技术，显著提升复杂环境下的通信稳定性和抗干扰能力。该技术通过分级优化信号处理流程，解决了实际信道噪声对量子态传输的干扰问题，为量子通信的工程化应用奠定基础。团队在量子安全直接通信领域实现突破，提出动态密钥分发与抗量子攻击算法，保障通信链路在量子计算威胁下的长期安全性。研究成果发表于国际权威期刊《Research》，并获行业高度关注。

2. **必要支撑条件**项目总投资2030万，需专项资助经费780万，自筹资金1250万。其中主要包括用于芯片、模组、无人机系统安全升级开发测试用电路板、电子元器件、样机等耗材200万；无人机专用安全管控芯片(40nm)样品流片、封装等测试化验加工费312万。研发人员劳务费和专家咨询费220万，自筹600万。设备方面全部自筹，约650万。
3. **项目负责人科研能力**项目负责人1996年毕业于东北大学计算机系计算机应用系，1999年至2014创办陕西诚泰科技讯息有限责任公司担任总经理，2016年创办西安文渊软件科技有限公司任总经理。做为项目负责人完成过西交网络空间安全研究院信创计算机管控系统开发项目、深圳鹏城实验室FABS处理器后端设计、流片与封装测试采购项目、西交大创新港网络建设物联设备项目、蓝田县综治视联网系统平台及网络技术服务项目、创新港信息化四网融合设备系统项目、综治视联网系统建设项目等。
4. **项目团队情况**
   西安文渊科技有限公司具备军密二级资质以及ISO质量体系认证证书，在军工项目及系统工程项目方面具备良好能力，在系统集成、平台搭建、技术融合方面有着丰富经验。其中基于国产信创体系的智慧信息化产品得到了广泛应用，如物联信息化管理平台、云边端一体化安全管控平台等公司与多所高校及科研院所建立了产学研的合作关系，其中与西交大在物联安全管控及物联场景实现技术创新领域紧密合作，自主研发的基于物联安全管控系统入选陕西省军民融合产品名目。团队拥有业内主流的芯片开发环境，从芯片架构设计、仿真、验证到接口驱动移植有完整的开发团队；有完整的产业链配套能力，与业内EDA设计企业、IPcore提供商、测试机构有良好的合作和资源，拥有数字芯片的规模量产和应用支持能力。公司与多所高校及科研院所建立了产学研的合作关系，其中与西安交大院士团队在物联安全管控及物联场景实现技术创新领域紧密合作，自主研发的基于安全管控芯片的物联安全管控系统入选陕西省军民融合产品名目。

西交网络空间安全研究院由浙江省诸暨市人民政府与西安交通大学于2023年5月签约共建，2023年11月揭牌运行。研究院性质为注册类事业单位人，由诸暨市人民政府举办。中国科学院院士、西安交通大学电子与信息学部主任管晓宏担任院长和首席科学家。研究院面向网络与数据安全领域科技发展前沿，大力开展网络安全自主可控专用芯片、软硬结合内外网安全管控系统以及行业安全管控整体解决方案等网络空间安全关键核心技术研究，建设有安全专用芯片研究中心、安全管控系统研究中心、行业解决方案研究中心等三个研究中心，打造集科学研究、成果转化、人才引育、产业孵化于一体的新型研发机构。研究院现有常驻工作人员25人，其中博士1人、硕士5人；2023年入选国家"火炬计划"人才项目1人。2023年与杭州海康威视数字技术股份有限公司、浙江理工大学共建的"全省智能物联网络与数据安全重点实验室"获得认定，成为浙江省首批获得认定的15个全省重点实验室之一。

西北大学是综合性全国重点大学，有24个博士学位授权点、57个硕士学位授权点，24个博士后流动站。西北大学搭建了全球首个基于高斯映射的CV-QSDC实验平台，验证了连续变量量子通信协议的有效性，并提出了信道参数估计与信号分级处理技术，显著提升复杂环境下的通信稳定性和抗干扰能力。

浙江唯识智能科技有限公司的母公司是厦门唯识筋斗云科技有限公司，技术团队自2012年起从事低空设备硬件研发，是大疆公司的首批合作伙伴。专注于"低空数字系统+AIOT"产品应用，浙江唯识则基于该技术框架，提供无人机城市治理、警务创新等场景的解决方案。侧重技术研发，拥有35个软件著作权和4项专利，聚焦低空算法仓系统、大载重无人机等底层技术。定位应用市场，承接厦门公司的技术成果，落地智能无人机巡查、5G+空地一体治理等实际场景。

专注于"低空数字系统+AIOT"产品应用，以人工智能技术为核心，集成自主研发边缘计算设备，构建基于数字视网膜技术的低空算法仓平台，提供全空间GIS框架下的基于"端边云"创新架构的SaaS低空数字综合应用系统。在无人机自动控制、AI视频识别、数据智能分析领域有领先的技术手段，包括基于无人系统协同控制的无人机自主控制算法、基于数字视网膜技术的低空算法仓、基于数字视网膜技术的智慧低空算法仓、基于特征提取、语义分割的智能识别算法、基于低空数据驱动的无人机指挥调度决策模型。

##### （五）申请单位

1. **单位简况(生产经营及科研情况、资产及经济状况等)**
   西安文渊软件科技有限公司是一家从事集成电路设计、智能控制系统集成、软件开发等业务公司，成立于2016年10月18日，公司坐落在陕西省西安市高新区高新一路创新大厦N302室。

公司经营范围:集成电路设计；工业互联网数据服务；智能控制系统集成；软件开发；网络与信息安全软件开发；大数据服务；物联网技术服务；数据处理和存储支持服务；信息技术咨询服务；信息系统运行维护服务；信息系统集成服务；软件外包服务；网络设备销售；计算机软硬件及辅助设备批发；工程管理服务；移动通信设备制造；通信设备制造；物联网设备制造。

公司通过了高新技术企业认定，ISO 9001质量体系认证，军工涉密认证等，具有较为成熟的研发、生产、管理体系。

公司依托院士团队，在高校、高职，军民融合等领域有较为成熟的客户基础，围绕智慧校园、智慧后勤、绿色低碳园区、能源分级计量、终端安全管控等物联网平台、安全终端的市场应用方向，面向系统国产替代，打造值得信赖的企业形象。

2. **技术创新**
   西安文渊科技有限公司具备军密二级资质以及ISO质量体系认证证书，在军工项目及系统工程项目方面具备良好能力，在系统集成、平台搭建、技术融合方面有着丰富经验。其中基于国产信创体系的智慧信息化产品得到了广泛应用，如物联信息化管理平台、云边端一体化安全管控平台等公司与多所高校及科研院所建立了产学研的合作关系，其中与西交大在物联安全管控及物联场景实现技术创新领域紧密合作，自主研发的基于物联安全管控系统入选陕西省军民融合产品名目。团队拥有业内主流的芯片开发环境，从芯片架构设计、仿真、验证到接口驱动移植有完整的开发团队；有完整的产业链配套能力，与业内EDA设计企业、IPcore提供商、测试机构有良好的合作和资源，拥有数字芯片的规模量产和应用支持能力。公司与多所高校及科研院所建立了产学研的合作关系，其中与西安交大院士团队在物联安全管控及物联场景实现技术创新领域紧密合作，自主研发的基于安全管控芯片的物联安全管控系统入选陕西省军民融合产品名目。

公司已经取得知识产权证书共计40项有余，其中由国家版权局授权颁发的计算机软件著作权登记证书38项，国家知识产权局授权颁发的实用新型专利证书2项；集成电路布图设计登记证书1项，并通过ISO9001:2015质量管理体系认证，于2017年度获得"软件产品评估"和"软件企业评估"的"双软企业"证书，随着工作的进一步发展，我们仍会继续申请相关专利和计算机软件著作权等，及时保护公司的知识产权。

3. **战略合作**
   公司与全国多所高校有着良好的合作关系，公司产品先后进入"西安交通大学"、"西北工业大学"、"西安电子科技大学"、"湖南大学""四川大学"等。近年来公司为物联网平台设有专业的物联测试实验室，并于西安交大智能网络实验室、华为·OPENLAB实验室等测试机构及中国电子科技集团第20研究所、黄河电子集团(原国营786厂)在嵌入式系统开发和PCB版卡设计有深入的合作。

2024年营业收入:21724336.14元，利润总额259.29，16.13%。

##### （六）项目实施计划

项目实施年限，年度计划安排与阶段目标，具体考核指标。项目实施管理措施，企业参与程度、产学研联合机制(重点阐述产学研联合模式、具体参与的企业及企业参与方式和投入方式)，项目、人才、基地统筹计划，其他必要的支撑和配套条件落实情况。

1. **项目时间安排与阶段目标**:
   项目整体耗时2年，完成用于无人机系统可信执行环境的安全管控芯片、可信嵌入式操作系统、系统集成与可信安全防护技术验证平台、安全私有协议开发与嵌入式环境实现测试、无人机系统产品安全融合升级并验证应用、产学研基地建设与知识成果六个核心任务。

- **任务一:用于无人机系统可信执行环境的安全管控芯片应用研究**承担单位:西交网络空间安全研究院研究内容: 具备高性能数据处理、高密文数据吞吐、独有安全存储机制、CPU/总线/中断/内存安全访问设计，支持RSA2048、RSA4096,SHA256,SHA512,AES-CBC,AES-ECB等加解密算法硬件加速，支持SM2,SM3,SM4等国密算法硬件加速。子任务包括:芯片加密算法硬件加速、高限制写存贮区保护机制、动态真随机发生器、独立时间防护机制、安全模式下的访问机制。时间安排:2025.5.1~2026.5.1指标:(1) 支持国际与国密加解密算法，实现硬件加速达成高于1Gbps的数据吞吐带宽。(2) 安全访问设计:对CPU、总线、中断、内存等模块实施"不可绕过"的安全访问设计。(3) 并建立安全存储机制以防止侵入和篡改，引入非法攻击缓存安全区数据擦除机制，支持硬件级电子围栏功能。
- **任务二:用于无人机系统可信执行环境的安全可信嵌入式操作系统**承担单位:西交网络空间安全研究院研究内容:自研嵌入式可信安全操作系统，实现安全与自主可控。在CPU上同时隔离运行非安全核(任务核)和安全核(管控核)。普通世界中的代码在非安全核上运行，只能访问普通世界的硬件资源，运行通用操作系统，主要负责普通应用任务的执行；安全世界中的代码在安全核上运行，能够访问所有硬件资源，运行安全操作系统，主要负责管控任务的运行。时间安排:2025.5.1~2026.5.1指标:系统支持芯片级嵌入式可信执行环境安全防护、基于固有安全技术的轻量级可执行安全管控机制，可支撑实现无人机系统设备安全多要素(访问认证/时间/地点等多维度实时管控)和无人机系统网络空间安全，TEE系统轻量化，TEE镜像占用固态存储空间小于3MB，运行空间小于12MB(不含TA)，TEE和REE切换平均时间小于1ms，TEE启动速度小于0.8秒。
- **任务三:系统集成与可信安全防护技术验证平台**承担单位:西安文渊软件科技有限公司协助单位:浙江唯识智能科技有限公司、西交网络空间安全研究院、西北大学研究内容:芯片级安全可信系统与无人机企业的传统管理系统相结合，用于数据采集，监控和全系统运行，并进行应用验证。多维感知与数据采集技术方面多模态传感融合，AI实时图像处理通过深度学习算法自动识别异常目标。具备实时态势感知界面，集成三维地图、动态热力图等可视化工具，实时展示无人机飞行状态及威胁分布。远程操作与日志分析等支持一键式反制操作，并提供数据回溯、异常事件分析等管理功能，飞行日志、传感器数据实时上链存储，确保数据完整性和可追溯性，防范篡改风险。硬件级可信执行环境(TEE)隔离关键飞行控制模块，通过安全芯片保护核心算法免受恶意攻击。时间安排:2025.10.1~2026.10.1指标:系统支持芯片级嵌入式可信执行环境安全防护、基于固有安全技术的轻量级可执行安全管控机制，可支撑实现无人机系统设备安全多要素(访问认证/时间/地点等多维度实时管控)和无人机系统网络空间安全，TEE系统轻量化，TEE镜像占用固态存储空间小于3MB，运行空间小于12MB(不含TA)，TEE和REE切换平均时间小于1ms，TEE启动速度小于0.8秒。
- **任务四:安全私有协议开发与嵌入式环境实现测试**承担单位:西北大学协助单位:西交网络空间安全研究院研究内容:设计基于Diffie-Hellman密钥交换的轻量级传输层安全私有加密协议。它支持多种加密算法，并独特支持国密算法，提供完整的加密通信、身份验证与完整性保护。
- 集成SM2,SM3,SM4国密算法
- 模块化的协议框架
- 可构建定制化的安全通信方案
- 双向DH密钥交换机制
- 支持多种不同类型DH函数
- 多种不同类型的加密算法哈希函数时间安排:2025.5.1~2026.10.1指标:安全通信协议支持基于形式化方法的建模与验证，能够抵御欺骗攻击和重放攻击等网络攻击的能力，安全性达到128bit级别以上，具备检测无人机控制参数组合错误的能力。
- **任务五:无人机系统产品安全融合升级并验证应用**承担单位:浙江唯识智能科技有限公司协助单位:西安文渊软件科技有限公司、西交网络空间安全研究院、西北大学研究内容:推出高安全等级物流无人机(集成芯片级可信防护系统、安全私有协议)。将自主控制算法、低空算法仓与安全协议结合，构建"端-边-云"三位一体安全架构，降低黑飞风险30%；在试验场完成系统级测试验证。时间安排:2025.12.30~2027.5.1指标:无人机系统的安全保障，防范对无人机防镜像攻击、恶意软件植入攻击、非法指令启动攻击及关键部件替换攻击；基于可信执行环境的安全加解密，性能满足验证平台需求。
- **任务六:产学研基地建设与知识成果**
  承担单位:西交网络空间安全研究院、西北大学
  协助单位:西安文渊软件科技有限公司、浙江唯识智能科技有限公司
  内容:为保障技术研发与产业需求深度对接，项目在西交网络空间安全研究院与西北大学设立联合实验室与产学研基地，共享芯片开发平台、协议验证工具及仿真测试环境。开设"无人机安全技术""安全芯片设计"等实践课程，采用"攻关课题+实战项目"教学模式，由企业工程师与高校导师联合授课。文渊公司与唯识智能公司指导学生参与安全验证平台搭建、芯片设计验证测试等实际操作。通过参与唯识智能的产业化流程，培养学生从研发到市场的全链条思维。西交研究院主导硬件级TEE研发，西北大学提供协议验证工具，文渊公司整合方案，唯识智能推动产业化；每年组织2次产学研技术交流会，促进研发与市场需求对接。
  时间安排:2025.5.1~2027.5.1
  指标:开设校企联合课程"无人机安全技术"，每年培养3名以上复合型人才；每年提供10个实习岗位，学生参与安全验证平台搭建、芯片集成测试等实际项目；设立专项奖学金，选拔5名优秀学生深度参与技术攻关。
  完成无人机系统安全升级融合和技术验证平台，推出高安全等级无人机实践验证。

知识产权成果:

- 申请发明专利2项、实用新型1项(涵盖无人机安全管控芯片、可信执行环境方向等)；
- 登记软件著作权或版图共5项(包括空域管理平台、安全验证工具链)；
- 形成技术秘密1项(涉及TEE性能优化、攻击模拟算法等)。

2. **项目统筹计划及产学研联合**:
   项目依托西交网络空间安全研究院、西北大学的科研优势，联合西安文渊科技有限公司、浙江唯识智能公司的集成能力和产业资源，聚焦无人机安全核心领域，构建"产学研用"一体化协同创新体系，致力于突破核心技术瓶颈，推动安全防护技术的自主可控与产业化落地，与人才培养深度融合。项目以"优势互补、协同攻坚"为原则，整合四方核心资源，依托西交网络空间安全研究院安全芯片设计、安全嵌入式操作系统研发优势，主导硬件级可信执行环境(TEE)构建，为无人机端侧数据隔离与实时防护提供底层支撑；西北大学新型网络智能信息服务国家地方联合工程研究中心(国家级)与西安市影像组学与智能感知重点实验室(省级)联合，聚焦安全协议形式化验证与密码算法优化，提供理论模型与实验环境，构建可信安全私有传输协议，并配合完成在嵌入式环境中实现，确保通信协议的逻辑完备性与抗攻击能力；文渊公司负责技术方案整合、项目集成和总体协调调度，并联合浙江唯识智能公司升级传统管理平台为高技术高成熟度的无人机系统协同可信安全防护技术验证平台。无人机企业唯识科技负责安全芯片系统与无人机系统的融合集成，解决软硬件协同适配难题。并产业化经验，推动技术成果向高可靠无人机产品的转化，开拓国防与民用市场。

为保障技术研发与产业需求深度对接，项目在西交网络空间安全研究院与西北大学设立联合实验室与产学研基地，共享芯片开发平台、协议验证工具及仿真测试环境。开设"无人机安全技术""安全芯片设计"等实践课程，采用"攻关课题+实战项目"教学模式，由企业工程师与高校导师联合授课。文渊公司与唯识智能公司投资搭建芯片设计验证测试、安全嵌入式系统、安全验证平台设计等学习及实践环境，配套相关工具或设备，指导学生参与项目实际操作。每年为学生提供10个以上实习岗位，通过参与唯识智能的产业化流程，培养学生从研发到市场的全链条思维。

##### （七）其它说明

无

23/99

### 九、项目绩效目标

| 一级指标类别 | 二级指标类别 | 明细指标                | 预期绩效目标 |
| ------------ | ------------ | ----------------------- | ------------ |
|              | 知识产权     | 1、专利授权数(项)       | 3            |
|              |              | (1)授权发明专利         | 2            |
|              |              | (2)实用新型             | 1            |
|              |              | (3)外观设计             | 0            |
|              |              | 2、软件著作权授权数(项) | 5            |
|              |              | 3、发表论文(篇)         | 0            |
|              |              | (1)其中SCI索引收录数    | 0            |
| 产出类指标   |              | (2)其中EI索引收录数     | 0            |
|              |              | (3)其它                 | 0            |
|              |              | 4、著作(部)             | 0            |
|              |              | 5、制订标准数(项)       | 5            |
|              |              | (1)国际标准             | 0            |
|              |              | (2)国家标准             | 0            |
|              |              | (3)行业标准             | 0            |
|              |              | (4)地方标准             | 0            |
|              |              | (5)企业标准             | 0            |
|              | 其他成果     | (6)科技报告             | 5            |
|              |              | 1、填补技术空白数       | 0            |
|              |              | (1)国际                 | 0            |
|              |              | (2)国家                 | 0            |
|              |              | (3)省级                 | 0            |
|              |              | 2、获奖项数             | 0            |
|              |              | (1)国家奖项             | 0            |
|              |              | (2)部、省奖项           | 0            |
|              |              | (3)地市级奖项           | 0            |
|              |              | 3、其他科技成果产出     | 3            |
|              |              | (1)新工艺(或新方法模式) | 0            |
|              |              | (2)新产品(含农业新品种) | 2            |
|              |              | (3)新材料               | 0            |
|              |              | (4)新装备(装置)         | 0            |
|              |              | (5)平台/基地/示范点     | 1            |
|              |              | (6)中试线               | 0            |
|              |              | (7)生产线               | 0            |

38/99

|            |            | 4、研究开发情况                   | \                                                                                                                                                                                |
| ---------- | ---------- | --------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
|            |            | (1)小试                           | 是                                                                                                                                                                               |
|            |            | (2)中试(样品样机)                 | 是                                                                                                                                                                               |
|            |            | (3)小批量                         | 否                                                                                                                                                                               |
|            |            | (4)规模化生产                     | 否                                                                                                                                                                               |
|            | 人才引育   | 1、引进高层次人才                 | 0                                                                                                                                                                                |
|            |            | (1)博士、博士后                   | 0                                                                                                                                                                                |
|            |            | (2)硕士                           | 0                                                                                                                                                                                |
|            |            | 2、培养高层次人才                 | 3                                                                                                                                                                                |
|            |            | (1)博士、博士后                   | 1                                                                                                                                                                                |
|            |            | (2)硕士                           | 2                                                                                                                                                                                |
|            |            | 3、培训从事技术创新服务人员(人次) | 10                                                                                                                                                                               |
|            |            | 4、是否设立科研助理岗位           | 否                                                                                                                                                                               |
|            |            | 1、开放共享仪器设备数(台/套/只等) | 23                                                                                                                                                                               |
|            |            | 2、科研仪器利用率(%)              | 60                                                                                                                                                                               |
|            | 产业化情况 | 3、孵化科技型企业(个)             | 0                                                                                                                                                                                |
|            |            | 4、转化科技成果(个)               | 3                                                                                                                                                                                |
|            | 经济效益   | 1、新增产值(万元)                 | 20000.00                                                                                                                                                                         |
|            |            | 2、新增销售(万元)                 | 12000.00                                                                                                                                                                         |
| 效果类指标 |            | 3、新增出口创汇(万美元)           | 0.00                                                                                                                                                                             |
|            |            | 4、新增利润(万元)                 | 4000.00                                                                                                                                                                          |
|            |            | 1、新增税收(万元)                 | 260.00                                                                                                                                                                           |
|            | 社会效益   | 2、新增就业人数                   | 200                                                                                                                                                                              |
|            |            | 3、就业培训(人次)                 | 200                                                                                                                                                                              |
|            |            | 4、带动农民增收(万元)             | 0.00                                                                                                                                                                             |
|            |            | 5、培训和指导科技服务(人次)       | 25                                                                                                                                                                               |
|            |            | 6、新增产业带动情况               | 无人机安全管控芯片与可信执行环境相关产品可以提高在医疗救援、消防等高风险场景的应用渗透率；同时带动无人机企业、信息安全企业、电子制造、高端装备制造、智能物流、精准农业能等行业。 |
|            |            | 7、技术集成示范(项)               | 5                                                                                                                                                                                |
|            |            | 8、建立示范基地(亩数)             | 0.00                                                                                                                                                                             |
|            |            | 9、节约资源能源                   | 精准化作业模式、电力巡检无人机节省油耗、高频次无损检测                                                                                                                           |
|            |            | 10、环保效益                      | 污染源头管控、动态环境监测。                                                                                                                                                     |

39/99

| 其他需要说明的情况 | 应用场景20%的产品升级换代。同时依托产学研用基地，带动无人机供应链包括集成电路、芯片制造、软件和算法开发等产业链聚集，在西交大和西北大学展开低空经济安全领域长期人才培养计划。最终在形成陕西省低空经济安全产业名片和产业园区。以芯片级安全防护体系的可信执行环境带动无人机安全领域全面升级，5年内完成安全 |
| ------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

40/99

### 十、附件清单

| 序号 | 材料名称                                               | 是否必备 | 下载                                                                             |
| ---- | ------------------------------------------------------ | -------- | -------------------------------------------------------------------------------- |
| 1    | 合作协议                                               | 否       | 西交网络空间研究院合作协议.pdf,浙江唯识智能科技有限公司合作协议.pdf,合作协议.pdf |
| 2    | 平台资质证明及依托合作协议                             | 否       | 合作协议与资质证明.pdf                                                           |
| 3    | 相关科研成果、专利等知识产权证明材料                   | 是       | 科研成果与专利.pdf                                                               |
| 4    | 项目配套资金来源(如贷款、地方部门匹配资金等)的证明材料 | 是       | 无.pdf                                                                           |
| 5    | 中试或产业化所需相关产品生产的许可证明文件             | 否       |                                                                                  |
| 6    | 与项目相关的其他证明材料或文件等                       | 否       |                                                                                  |
| 7    | 其他附件                                               | 否       |                                                                                  |
| 8    | 2024年度资产负债表                                     | 是       | 2024年资产负债表.pdf                                                             |
| 9    | 2024年度利润表                                         | 是       | 2024年利润表.pdf                                                                 |
| 10   | 2024年度现金流量表                                     | 是       | 2024年现金流量表.pdf                                                             |
| 11   | 2024年度所有者权益变动表                               | 是       | 2024年所有者权益变动表.pdf                                                       |
| 12   | 2024年度第四季度银行存款账户对账单                     | 是       | 2024年第四季度对账单.pdf                                                         |

41/99

### 科研诚信承诺书

本人承诺在科研项目(课题)实施(包括项目(课题)申请、评估评审、检查、项目(课题)执行、资源汇交、验收等过程)中，遵守科学道德和诚信要求，严格执行项目(课题)管理规定和《项目(课题)任务合同书》中的约定，不发生下列科研不端行为:

1. 在职称、简历以及研究基础等方面提供虚假信息；
2. 抄袭、剽窃他人科研成果；
3. 捏造和篡改科研数据；
4. 违反科研伦理，在涉及基因、人体等研究中严格遵守有关规定和科研共识；
5. 不按时完成科研项目，违反科研经费管理相关规定；
6. 其他科研不端行为。

42/99

项目(课题)负责人签字:
日期:2025-03-25

### 十一、审核意见

#### 申请单位意见:

项目负责人签字:
单位负责人签字:
(单位盖章)
年 月 日

#### 推荐部门审查意见:

联系人:
办公电话:
通信地址:
(单位盖章)
年 月 日

43/99

### 面向无人机系统的云边端协同可信安全防护技术研究项目意向合作协议

**甲方**: 西安文渊软件科技有限公司
**乙方**:西交网络空间安全研究院

甲乙双方本着优势互补、互惠互利、资源共享，为了充分发挥各方的优势和特色，促进产学研用合作，提高合作水平，形成合作互动，决定开展"面向无人机系统的云边端协同可信安全防护技术研究"项目合作事宜，达成如下意向协议，待项目正式启动前补充正式合作协议。

#### 一、合作内容

甲乙双方依托公司和科研平台及人员基于"面向无人机系统的云边端协同可信安全防护技术研究"项目，进行产学研合作，为充分发挥合作效果，由甲方提供科研经费，乙方提供人员、技术和设备，甲方作为项目主办单位，负责技术方案整合、项目集成和总体协调调度，基于乙方安全管控芯片设计技术积累、安全嵌入式操作系统研发优势，主导硬件级可信执行环境(TEE)构建，为无人机端侧数据隔离与实时防护提供底层支撑。

项目目标:基于零信任理念，研究建立自主可控的芯片级安全防护体系，从硬件层面保护数据资源受到的非授权访问、篡改或盗取，实现数据和通道两方面的安全及管控。应用于无人机可信执行环境安全防护、匿名无线通信安全防护、无人机飞行控制异常检测、抵御欺骗攻击和重放攻击等网络攻击。自研安全管控芯片，具备高性能数据处理、高密文数据吞吐、独有安全存储机制、CPU/总线/中断/内存安全访问设计，支持RSA2048、RSA4096, SHA256, SHA512, AES-CBC, AES-ECB等加解密算法硬件加速，支持SM2, SM3, SM4等国密算法硬件加速，加解密性能不低于1000Mbps。系统支持芯片级嵌入式可信执行环境安全防护、基于固有安全技术的轻量级可执行安全管控机制，可支撑实现无人机系统设备安全多要素和无人机系统网络空间安全，TEE系统轻量化，TEE镜像占用固态存储空间小于3MB，运行空间小于12MB(不含TA)，TEE和REE切换平均时间小于1ms，TEE启动速度小于0.8秒。

#### 二、甲方的权利义务

1. 甲方根据科研成果转化情况，主动积极与乙方沟通，在不影响乙方正常工作的情况下，使用乙方科研平台开展科研成果转化工作。
2. 甲方支持乙方的产学研基地建设，根据"面向无人机系统的云边端协同可信安全防护技术研究"项目的科研需求，可为乙方的学生提供实习内容，指导实习学生，安排学生参与企业的项目开发和实践等。
3. 甲方依托乙方的科研平台，为"面向无人机系统的云边端协同可信安全防护技术研究"项目开展研究工作提供经费支持。
4. 甲方对合作项目中乙方的研究成果部分进行验收。

#### 三、乙方的权利义务

1. 乙方愿意提供技术、人员和设备为"面向无人机系统的云边端协同可信安全防护技术研究"项目服务。
2. 乙方根据甲方的申请，安排合适时间帮助甲方解决在项目开发中遇到的问题。
3. 在合作研究中，乙方负责以下:
   (1) 基于安全芯片方面的技术积累进一步创新，研发用于无人机可信执行环境的安全管控芯片，支撑无人机系统的安全。具备以下创新技术:

- 芯片加密算法硬件加速
- 高限制写存贮区保护机制
- 独立时间防护机制
- 基于芯片国密算法的安全启动
  (2) 基于乙方嵌入式操作系统的技术积累，研发用于无人机可信执行环境的专用嵌入式安全操作系统。创新性体现在:提出基于动态权重的多因子认证模型，结合时间、地理位置、设备指纹等动态因子，实现自适应安全等级调整；设计轻量级安全世界切换协议，通过硬件加速(如ARM TrustZone扩展指令集)减少切换开销；引入安全存储区的分层加密机制，结合国密算法(如SM4)实现数据分片加密存储，支持细粒度访问控制；创新性地将可信执行环境(TEE)与区块链技术结合，确保操作日志的不可篡改性和可追溯性。基于TLS的协议，进行轻量型的专用协议定制化，保证通信协议的安全性。
  (3) 配合甲方完成芯片级安全系统与无人机系统的集成融合，形成可信执行环境，达到相应技术指标，解决软硬件协同适配难题。

#### 四、科研成果归属

甲、乙双方在面向无人机系统的云边端协同可信安全防护技术合作研究形成的相关成果，双方共享，由甲方进行产业化转化。
