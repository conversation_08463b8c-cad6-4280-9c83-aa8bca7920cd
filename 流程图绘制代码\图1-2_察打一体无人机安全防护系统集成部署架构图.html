<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图1-2 察打一体无人机安全防护系统集成部署架构图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的实现
         * 重点展示系统集成部署架构和端到端连接关系
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-ground-station: #1e40af; /* 地面控制站-蓝色 */
            --color-security-module: #dc2626; /* 安全模组-红色 */
            --color-transmission: #059669; /* 传输链路-绿色 */
            --color-uav-platform: #7c3aed; /* 无人机平台-紫色 */
            --color-interface: #ea580c; /* 接口连接-橙色 */
            
            --color-border-light: #e5e7eb;
            --color-border-strong: #374151;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-bg-ground: #eff6ff;
            --color-bg-transmission: #ecfdf5;
            --color-bg-uav: #f3f4f6;
            --color-bg-security: #fef2f2;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --shadow-strong: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 8px;
            --line-color: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .flowchart-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-ground-station);
        }

        .flowchart-main-container {
            position: relative;
            width: 1200px;
            height: auto;
            background-color: var(--color-bg-main);
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        .system-section {
            border: 2px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-subtle);
        }

        .ground-station-section {
            background-color: var(--color-bg-ground);
            border-color: var(--color-ground-station);
        }

        .uav-platform-section {
            background-color: var(--color-bg-uav);
            border-color: var(--color-uav-platform);
        }

        .transmission-section {
            background-color: var(--color-bg-transmission);
            border: 3px solid var(--color-transmission);
            text-align: center;
            padding: 20px;
        }

        .section-title {
            font-family: var(--font-heading);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .ground-station-section .section-title {
            color: var(--color-ground-station);
        }

        .uav-platform-section .section-title {
            color: var(--color-uav-platform);
        }

        .transmission-section .section-title {
            color: var(--color-transmission);
            margin-bottom: 15px;
        }

        .subsystem-title {
            font-family: var(--font-heading);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            color: var(--color-text-dark);
        }

        .layer {
            display: flex;
            align-items: stretch;
            gap: 25px;
            justify-content: space-around;
        }

        .flow-stage {
            background-color: var(--color-bg-main);
            border: 2px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 16px 20px;
            box-shadow: var(--shadow-subtle);
            display: flex;
            flex-direction: column;
            text-align: center;
            flex: 1;
            max-width: 280px;
        }

        .security-module {
            border: 3px solid var(--color-security-module);
            background-color: var(--color-bg-security);
            box-shadow: 0 4px 8px rgba(220, 38, 38, 0.15);
        }

        .stage-title {
            font-family: var(--font-heading);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.8rem;
            color: var(--color-text-dark);
        }

        .stage-subtitle {
            font-size: 0.85rem;
            color: var(--color-text-light);
            margin-bottom: 0.8rem;
        }

        .stage-content-list {
            list-style: none;
            font-size: 0.85rem;
            line-height: 1.4;
            color: var(--color-text-light);
            text-align: left;
            padding-left: 16px;
        }
        
        .stage-content-list li {
            position: relative;
            margin-bottom: 3px;
        }

        .stage-content-list li::before {
            content: "•";
            position: absolute;
            left: -12px;
            color: var(--color-transmission);
        }

        .interface-indicator {
            position: absolute;
            top: 50%;
            right: -60px;
            transform: translateY(-50%);
            background-color: var(--color-interface);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .interface-indicator.left {
            left: -60px;
            right: auto;
        }

        .transmission-specs {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .spec-item {
            background-color: var(--color-bg-main);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid var(--color-transmission);
        }

        .spec-label {
            font-weight: 600;
            color: var(--color-transmission);
            font-size: 0.85rem;
        }

        .spec-value {
            color: var(--color-text-dark);
            font-size: 0.9rem;
            margin-top: 2px;
        }

        .bus-section {
            margin: 20px 0;
            text-align: center;
        }

        .bus-container {
            background-color: var(--color-bg-main);
            border: 2px solid var(--color-interface);
            border-radius: var(--border-radius);
            padding: 12px;
            display: inline-block;
        }

        .bus-title {
            font-weight: 600;
            color: var(--color-interface);
            margin-bottom: 5px;
        }

        .bus-spec {
            font-size: 0.85rem;
            color: var(--color-text-light);
        }

        .subsystems-layer {
            margin-top: 15px;
        }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        /* 响应式设计 */
        @media (max-width: 1280px) {
            .flowchart-main-container {
                width: 95%;
                max-width: 1200px;
            }
            
            .layer {
                flex-wrap: wrap;
            }
            
            .flow-stage {
                max-width: none;
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="flowchart-title">图1-2 察打一体无人机安全防护系统集成部署架构图</div>
    
    <div class="flowchart-main-container" id="myFlowchartContainer">
        <svg class="connector-canvas" id="connectorCanvas">
            <defs>
                <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--line-color)" />
                </marker>
                <marker id="arrowhead-transmission" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="8" markerHeight="6"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-transmission)" />
                </marker>
            </defs>
        </svg>

        <!-- 地面控制站系统 -->
        <div class="system-section ground-station-section" id="groundStationSection">
            <div class="section-title">地面控制站系统集成架构</div>
            <div class="subsystem-title">地面控制站航电系统</div>
            
            <div class="layer">
                <div class="flow-stage" id="commandControlConsole">
                    <div class="stage-title">指挥控制台</div>
                    <ul class="stage-content-list">
                        <li>任务规划/监控</li>
                        <li>态势显示/控制</li>
                        <li>操作员接口</li>
                    </ul>
                    <div class="interface-indicator">PCIe 3.0<br>(8Gbps)</div>
                </div>
                
                <div class="flow-stage security-module" id="groundSecurityModule">
                    <div class="stage-title">"赛安"安全模组</div>
                    <div class="stage-subtitle">(核心安全处理)</div>
                    <ul class="stage-content-list">
                        <li>统一安全服务</li>
                        <li>加密/认证/检测</li>
                        <li>策略管理</li>
                    </ul>
                    <div class="interface-indicator">千兆以太网<br>(1Gbps)</div>
                </div>
                
                <div class="flow-stage" id="dataLinkStation">
                    <div class="stage-title">数据链基站</div>
                    <ul class="stage-content-list">
                        <li>高功率发射</li>
                        <li>L/C双频段</li>
                        <li>数据链协议</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 端到端安全数据链传输 -->
        <div class="transmission-section" id="transmissionSection">
            <div class="section-title">端到端安全数据链传输</div>
            
            <div class="transmission-specs">
                <div class="spec-item">
                    <div class="spec-label">SM4硬件流加密</div>
                    <div class="spec-value">≥2Gbps</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">密钥协商时间</div>
                    <div class="spec-value">&lt;100ms</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">TEE安全隔离</div>
                    <div class="spec-value">&lt;1ms切换</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">通信安全等级</div>
                    <div class="spec-value">≥128bit</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">传输距离</div>
                    <div class="spec-value">≥50km</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">威胁检测准确率</div>
                    <div class="spec-value">&gt;90%</div>
                </div>
            </div>
        </div>

        <!-- 察打一体无人机平台 -->
        <div class="system-section uav-platform-section" id="uavPlatformSection">
            <div class="section-title">察打一体无人机平台</div>
            <div class="subsystem-title">机载航电系统集成架构</div>
            
            <div class="layer">
                <div class="flow-stage" id="flightControlComputer">
                    <div class="stage-title">主飞控计算机</div>
                    <div class="stage-subtitle">(FCC)</div>
                    <ul class="stage-content-list">
                        <li>ARM Cortex-A78</li>
                        <li>1KHz实时控制</li>
                        <li>飞控/导航/稳定</li>
                    </ul>
                    <div class="interface-indicator left">PCIe 3.0<br>(8Gbps)</div>
                </div>
                
                <div class="flow-stage security-module" id="uavSecurityModule">
                    <div class="stage-title">"赛安"安全模组</div>
                    <div class="stage-subtitle">(安全协处理)</div>
                    <ul class="stage-content-list">
                        <li>硬件级安全服务</li>
                        <li>&lt;1ms TEE切换</li>
                        <li>威胁检测/防护</li>
                    </ul>
                    <div class="interface-indicator">PCIe 3.0<br>(8Gbps)</div>
                </div>
                
                <div class="flow-stage" id="missionComputer">
                    <div class="stage-title">任务计算机</div>
                    <div class="stage-subtitle">(MC)</div>
                    <ul class="stage-content-list">
                        <li>Intel Core i7</li>
                        <li>任务处理</li>
                        <li>载荷/火控/规划</li>
                    </ul>
                </div>
            </div>

            <!-- 机载数据总线 -->
            <div class="bus-section">
                <div class="bus-container" id="aviationDataBus">
                    <div class="bus-title">机载数据总线</div>
                    <div class="bus-spec">(ARINC 429)</div>
                    <div class="bus-spec">1Mbps/双冗余</div>
                </div>
            </div>

            <!-- 子系统层 -->
            <div class="layer subsystems-layer">
                <div class="flow-stage" id="communicationModule">
                    <div class="stage-title">通信模块</div>
                    <div class="stage-subtitle">(CM)</div>
                    <ul class="stage-content-list">
                        <li>数据链处理</li>
                        <li>安全协议</li>
                    </ul>
                </div>
                
                <div class="flow-stage" id="navigationModule">
                    <div class="stage-title">导航模块</div>
                    <div class="stage-subtitle">(GNSS/INS)</div>
                    <ul class="stage-content-list">
                        <li>GPS/北斗双模</li>
                        <li>5m定位精度</li>
                    </ul>
                </div>
                
                <div class="flow-stage" id="payloadSystem">
                    <div class="stage-title">载荷系统</div>
                    <div class="stage-subtitle">(Payload)</div>
                    <ul class="stage-content-list">
                        <li>光电/红外/激光</li>
                        <li>武器挂载</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('myFlowchartContainer');
            const svg = document.getElementById('connectorCanvas');

            const connections = [
                // 地面控制站内部连接
                { from: 'commandControlConsole', to: 'groundSecurityModule', fromPort: 'right', toPort: 'left', type: 'internal' },
                { from: 'groundSecurityModule', to: 'dataLinkStation', fromPort: 'right', toPort: 'left', type: 'internal' },
                
                // 地面站到传输层
                { from: 'dataLinkStation', to: 'transmissionSection', fromPort: 'bottom', toPort: 'top', type: 'transmission' },
                
                // 传输层到无人机
                { from: 'transmissionSection', to: 'uavSecurityModule', fromPort: 'bottom', toPort: 'top', type: 'transmission' },
                
                // 无人机内部连接
                { from: 'flightControlComputer', to: 'uavSecurityModule', fromPort: 'right', toPort: 'left', type: 'internal' },
                { from: 'uavSecurityModule', to: 'missionComputer', fromPort: 'right', toPort: 'left', type: 'internal' },
                
                // 数据总线连接
                { from: 'uavSecurityModule', to: 'aviationDataBus', fromPort: 'bottom', toPort: 'top', type: 'bus' },
                { from: 'aviationDataBus', to: 'communicationModule', fromPort: 'bottom', toPort: 'top', type: 'bus' },
                { from: 'aviationDataBus', to: 'navigationModule', fromPort: 'bottom', toPort: 'top', type: 'bus' },
                { from: 'aviationDataBus', to: 'payloadSystem', fromPort: 'bottom', toPort: 'top', type: 'bus' },
            ];
            
            function getPortPosition(element, portSide, containerRect) {
                const elemRect = element.getBoundingClientRect();
                const relX = elemRect.left - containerRect.left;
                const relY = elemRect.top - containerRect.top;
                
                switch(portSide) {
                    case 'top': return { x: relX + elemRect.width / 2, y: relY };
                    case 'bottom': return { x: relX + elemRect.width / 2, y: relY + elemRect.height };
                    case 'left': return { x: relX, y: relY + elemRect.height / 2 };
                    case 'right': return { x: relX + elemRect.width, y: relY + elemRect.height / 2 };
                    default: return { x: relX + elemRect.width / 2, y: relY + elemRect.height / 2 };
                }
            }
            
            function drawConnection(p1, p2, type = 'normal') {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                
                let pathData;
                if (type === 'internal') {
                    // 直线连接用于内部连接
                    pathData = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;
                } else if (type === 'bus') {
                    // 从总线到各子系统的连接
                    const midY = p1.y + (p2.y - p1.y) / 2;
                    pathData = `M ${p1.x} ${p1.y} V ${midY} H ${p2.x} V ${p2.y}`;
                } else {
                    // 弯曲连接用于跨层连接
                    const midY = p1.y + (p2.y - p1.y) / 2;
                    pathData = `M ${p1.x} ${p1.y} V ${midY} H ${p2.x} V ${p2.y}`;
                }

                path.setAttribute('d', pathData);
                path.setAttribute('fill', 'none');
                path.setAttribute('marker-end', type === 'transmission' ? 'url(#arrowhead-transmission)' : 'url(#arrowhead)');
                
                if (type === 'transmission') {
                    path.setAttribute('stroke', 'var(--color-transmission)');
                    path.setAttribute('stroke-width', '3');
                } else if (type === 'internal') {
                    path.setAttribute('stroke', 'var(--color-interface)');
                    path.setAttribute('stroke-width', '2');
                } else if (type === 'bus') {
                    path.setAttribute('stroke', 'var(--color-uav-platform)');
                    path.setAttribute('stroke-width', '1.5');
                } else {
                    path.setAttribute('stroke', 'var(--line-color)');
                    path.setAttribute('stroke-width', '1.5');
                }
                
                svg.appendChild(path);
            }

            function drawConnections() {
                // 清空旧连接线，保留defs
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                const containerRect = container.getBoundingClientRect();
                
                connections.forEach(conn => {
                    const fromEl = document.getElementById(conn.from);
                    const toEl = document.getElementById(conn.to);
                    if (!fromEl || !toEl) return;
                    
                    const p1 = getPortPosition(fromEl, conn.fromPort, containerRect);
                    const p2 = getPortPosition(toEl, conn.toPort, containerRect);
                    drawConnection(p1, p2, conn.type);
                });
            }

            const observer = new ResizeObserver(drawConnections);
            window.addEventListener('load', () => {
                setTimeout(drawConnections, 100); // 延迟确保布局完成
                observer.observe(container);
            });
        });
    </script>
</body>
</html>