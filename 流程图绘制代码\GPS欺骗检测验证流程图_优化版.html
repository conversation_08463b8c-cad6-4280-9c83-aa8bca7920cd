<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS欺骗检测验证流程图 - 优化版</title>
    <style>
        /*
         * 优化版GPS欺骗检测验证流程图
         * 设计理念：简洁、方正、重点突出
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 简化色彩体系 --- */
            --color-primary: #1e40af;
            --color-secondary: #059669;
            --color-accent: #ea580c;
            --color-core: #7c3aed;
            
            --color-border: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .flowchart-title {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
            text-align: center;
        }

        .flowchart-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 40px;
            width: 600px;
            height: 600px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-circle {
            position: relative;
            width: 500px;
            height: 500px;
        }

        .core-algorithm {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 160px;
            height: 160px;
            background: linear-gradient(135deg, var(--color-core), #a855f7);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            text-align: center;
            box-shadow: var(--shadow-subtle);
            z-index: 10;
        }

        .core-title {
            font-size: 1.1rem;
            margin-bottom: 4px;
        }

        .core-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .flow-step {
            position: absolute;
            width: 120px;
            height: 80px;
            background-color: var(--color-bg-main);
            border: 3px solid var(--color-primary);
            border-radius: var(--border-radius);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
            box-shadow: var(--shadow-subtle);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .flow-step:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .step-title {
            font-size: 0.85rem;
            color: var(--color-primary);
            margin-bottom: 4px;
        }

        .step-key {
            font-size: 0.75rem;
            color: var(--color-text-light);
        }

        /* 6个步骤的位置 */
        .step-1 { top: 0; left: 50%; transform: translateX(-50%); }
        .step-2 { top: 15%; right: 0; }
        .step-3 { bottom: 15%; right: 0; }
        .step-4 { bottom: 0; left: 50%; transform: translateX(-50%); }
        .step-5 { bottom: 15%; left: 0; }
        .step-6 { top: 15%; left: 0; }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .performance-indicators {
            position: absolute;
            bottom: -80px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            font-size: 0.75rem;
            color: var(--color-text-light);
        }

        .indicator {
            text-align: center;
        }

        .indicator-value {
            font-weight: 600;
            color: var(--color-accent);
            margin-bottom: 2px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .flowchart-container {
                width: 90vw;
                height: 90vw;
                max-width: 500px;
                max-height: 500px;
            }
            
            .flow-circle {
                width: 80%;
                height: 80%;
            }
            
            .core-algorithm {
                width: 120px;
                height: 120px;
            }
            
            .flow-step {
                width: 100px;
                height: 70px;
                font-size: 0.8rem;
            }
        }

        /* 动画效果 */
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .flow-arrow {
            stroke-dasharray: 8, 4;
            animation: rotate 10s linear infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        .flow-step {
            animation: fadeIn 0.6s ease forwards;
        }

        .flow-step:nth-child(1) { animation-delay: 0.1s; }
        .flow-step:nth-child(2) { animation-delay: 0.2s; }
        .flow-step:nth-child(3) { animation-delay: 0.3s; }
        .flow-step:nth-child(4) { animation-delay: 0.4s; }
        .flow-step:nth-child(5) { animation-delay: 0.5s; }
        .flow-step:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <div class="flowchart-title">GPS欺骗检测验证流程图</div>
    
    <div class="flowchart-container">
        <div class="flow-circle">
            <svg class="connector-canvas" id="connectorCanvas">
                <defs>
                    <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                            markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                            orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-primary)" />
                    </marker>
                </defs>
            </svg>

            <!-- 中心核心算法 -->
            <div class="core-algorithm">
                <div class="core-title">LSTM+GNN</div>
                <div class="core-subtitle">融合算法</div>
            </div>

            <!-- 6个流程步骤 -->
            <div class="flow-step step-1" id="step1">
                <div class="step-title">数据采集</div>
                <div class="step-key">多源传感器</div>
            </div>

            <div class="flow-step step-2" id="step2">
                <div class="step-title">特征提取</div>
                <div class="step-key">时空频域</div>
            </div>

            <div class="flow-step step-3" id="step3">
                <div class="step-title">模型推理</div>
                <div class="step-key">置信评估</div>
            </div>

            <div class="flow-step step-4" id="step4">
                <div class="step-title">威胁评估</div>
                <div class="step-key">风险等级</div>
            </div>

            <div class="flow-step step-5" id="step5">
                <div class="step-title">决策判断</div>
                <div class="step-key">阈值比较</div>
            </div>

            <div class="flow-step step-6" id="step6">
                <div class="step-title">响应执行</div>
                <div class="step-key">系统切换</div>
            </div>
        </div>

        <!-- 性能指标 -->
        <div class="performance-indicators">
            <div class="indicator">
                <div class="indicator-value">&gt;95%</div>
                <div>检测准确率</div>
            </div>
            <div class="indicator">
                <div class="indicator-value">&lt;30s</div>
                <div>渐进检测</div>
            </div>
            <div class="indicator">
                <div class="indicator-value">&lt;5s</div>
                <div>突发检测</div>
            </div>
            <div class="indicator">
                <div class="indicator-value">&lt;50ms</div>
                <div>系统响应</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const svg = document.getElementById('connectorCanvas');
            const container = svg.parentElement;
            
            // 计算圆形布局的连接线
            function drawCircularConnections() {
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                
                const centerX = 250;
                const centerY = 250;
                const radius = 180;
                
                // 6个步骤的角度位置
                const angles = [0, 60, 120, 180, 240, 300];
                
                for (let i = 0; i < 6; i++) {
                    const currentAngle = angles[i] * Math.PI / 180;
                    const nextAngle = angles[(i + 1) % 6] * Math.PI / 180;
                    
                    const x1 = centerX + radius * Math.cos(currentAngle);
                    const y1 = centerY + radius * Math.sin(currentAngle);
                    const x2 = centerX + radius * Math.cos(nextAngle);
                    const y2 = centerY + radius * Math.sin(nextAngle);
                    
                    // 绘制弧形连接线
                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    const midAngle = (currentAngle + nextAngle) / 2;
                    const arcRadius = radius + 30;
                    const midX = centerX + arcRadius * Math.cos(midAngle);
                    const midY = centerY + arcRadius * Math.sin(midAngle);
                    
                    const pathData = `M ${x1} ${y1} Q ${midX} ${midY} ${x2} ${y2}`;
                    path.setAttribute('d', pathData);
                    path.setAttribute('fill', 'none');
                    path.setAttribute('stroke', 'var(--color-primary)');
                    path.setAttribute('stroke-width', '2');
                    path.setAttribute('marker-end', 'url(#arrowhead)');
                    path.setAttribute('opacity', '0.7');
                    
                    svg.appendChild(path);
                }
            }

            // 添加交互效果
            const steps = document.querySelectorAll('.flow-step');
            steps.forEach((step, index) => {
                step.addEventListener('mouseenter', () => {
                    step.style.borderColor = 'var(--color-accent)';
                    step.style.transform = 'scale(1.1)';
                });
                
                step.addEventListener('mouseleave', () => {
                    step.style.borderColor = 'var(--color-primary)';
                    step.style.transform = 'scale(1)';
                });
            });

            // 初始化连接线
            setTimeout(drawCircularConnections, 100);
            
            // 响应式更新
            const observer = new ResizeObserver(drawCircularConnections);
            observer.observe(container);
        });
    </script>
</body>
</html>