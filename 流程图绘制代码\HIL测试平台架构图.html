<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIL测试平台架构图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的分层架构图实现
         * 核心设计理念：分层布局 + 双向连接 + 测试能力展示
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-primary: #1e40af; /* 主色调-蓝色 */
            --color-hardware: #1e40af; /* 真实硬件层-蓝色 */
            --color-simulation: #059669; /* 仿真环境层-绿色 */
            --color-monitoring: #7c3aed; /* 监控分析层-紫色 */
            --color-connection: #ea580c; /* 连接线-橙色 */
            
            --color-border-light: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-bg-hardware: #eff6ff;
            --color-bg-simulation: #ecfdf5;
            --color-bg-monitoring: #f3f4f6;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --shadow-strong: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .architecture-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
        }

        .architecture-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 40px;
            margin: 0 auto;
            max-width: 1200px;
            width: 100%;
            position: relative;
        }

        .layers-container {
            display: flex;
            flex-direction: column;
            gap: 40px;
            position: relative;
            margin-bottom: 40px;
        }

        .architecture-layer {
            border: 3px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-subtle);
            position: relative;
        }

        .hardware-layer {
            background-color: var(--color-bg-hardware);
            border-color: var(--color-hardware);
        }

        .simulation-layer {
            background-color: var(--color-bg-simulation);
            border-color: var(--color-simulation);
        }

        .monitoring-layer {
            background-color: var(--color-bg-monitoring);
            border-color: var(--color-monitoring);
        }

        .layer-title {
            font-family: var(--font-heading);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            padding: 10px 20px;
            border-radius: 6px;
            color: white;
        }

        .hardware-layer .layer-title {
            background-color: var(--color-hardware);
        }

        .simulation-layer .layer-title {
            background-color: var(--color-simulation);
        }

        .monitoring-layer .layer-title {
            background-color: var(--color-monitoring);
        }

        .layer-modules {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .module-card {
            background-color: var(--color-bg-main);
            border: 2px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-subtle);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-strong);
        }

        .hardware-layer .module-card {
            border-color: rgba(30, 64, 175, 0.3);
        }

        .simulation-layer .module-card {
            border-color: rgba(5, 150, 105, 0.3);
        }

        .monitoring-layer .module-card {
            border-color: rgba(124, 58, 237, 0.3);
        }

        .module-title {
            font-family: var(--font-heading);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--color-text-dark);
            text-align: center;
        }

        .module-subtitle {
            font-size: 0.85rem;
            color: var(--color-text-light);
            text-align: center;
            margin-bottom: 12px;
            font-style: italic;
        }

        .module-content {
            list-style: none;
            font-size: 0.85rem;
            line-height: 1.5;
            color: var(--color-text-light);
        }

        .module-content li {
            position: relative;
            margin-bottom: 4px;
            padding-left: 16px;
        }

        .module-content li::before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .hardware-layer .module-content li::before { color: var(--color-hardware); }
        .simulation-layer .module-content li::before { color: var(--color-simulation); }
        .monitoring-layer .module-content li::before { color: var(--color-monitoring); }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .capabilities-section {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, var(--color-bg-light) 0%, var(--color-bg-main) 100%);
            border-radius: var(--border-radius);
            border: 2px solid var(--color-primary);
        }

        .capabilities-title {
            font-family: var(--font-heading);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--color-primary);
            text-align: center;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .capability-item {
            background-color: var(--color-bg-main);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--color-primary);
            box-shadow: var(--shadow-subtle);
        }

        .capability-label {
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .capability-icon {
            font-size: 1.2rem;
            color: var(--color-primary);
        }

        .capability-value {
            font-size: 1rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 4px;
        }

        .capability-description {
            font-size: 0.85rem;
            color: var(--color-text-light);
            line-height: 1.4;
        }

        .features-section {
            margin-top: 30px;
            padding: 25px;
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            border: 2px solid var(--color-simulation);
        }

        .features-title {
            font-family: var(--font-heading);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-simulation);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .feature-item {
            padding: 15px;
            background-color: var(--color-bg-light);
            border-radius: var(--border-radius);
            border-left: 3px solid var(--color-simulation);
        }

        .feature-title {
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: 8px;
        }

        .feature-content {
            color: var(--color-text-light);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .layer-modules {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .architecture-container {
                padding: 20px;
            }
            
            .layer-modules {
                grid-template-columns: 1fr;
            }
            
            .capabilities-grid {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .architecture-layer {
            animation: fadeInUp 0.6s ease forwards;
        }

        .architecture-layer:nth-child(1) { animation-delay: 0.1s; }
        .architecture-layer:nth-child(2) { animation-delay: 0.3s; }
        .architecture-layer:nth-child(3) { animation-delay: 0.5s; }

        /* 连接线动画 */
        @keyframes connectionFlow {
            0% { stroke-dashoffset: 20; }
            100% { stroke-dashoffset: 0; }
        }

        .connection-line {
            stroke-dasharray: 8, 4;
            animation: connectionFlow 3s linear infinite;
        }
    </style>
</head>
<body>
    <div class="architecture-title">HIL测试平台架构图</div>
    
    <div class="architecture-container">
        <div class="layers-container" id="layersContainer">
            <svg class="connector-canvas" id="connectorCanvas">
                <defs>
                    <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                            markerUnits="strokeWidth" markerWidth="8" markerHeight="6"
                            orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-connection)" />
                    </marker>
                    <marker id="arrowhead-reverse" viewBox="0 0 10 10" refX="2" refY="5" 
                            markerUnits="strokeWidth" markerWidth="8" markerHeight="6"
                            orient="auto-start-reverse">
                        <path d="M 10 0 L 0 5 L 10 10 z" fill="var(--color-connection)" />
                    </marker>
                </defs>
            </svg>

            <!-- 真实硬件层 -->
            <div class="architecture-layer hardware-layer" id="hardwareLayer">
                <div class="layer-title">真实硬件层</div>
                <div class="layer-modules">
                    <div class="module-card">
                        <div class="module-title">飞控计算机</div>
                        <div class="module-subtitle">(FCC)</div>
                        <ul class="module-content">
                            <li>ARM A78</li>
                            <li>1KHz控制</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">通信模块</div>
                        <div class="module-subtitle">(CM)</div>
                        <ul class="module-content">
                            <li>通信处理器</li>
                            <li>安全协议</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">赛安模组</div>
                        <div class="module-subtitle">(Security)</div>
                        <ul class="module-content">
                            <li>TEE隔离</li>
                            <li>硬件加密</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 仿真环境层 -->
            <div class="architecture-layer simulation-layer" id="simulationLayer">
                <div class="layer-title">仿真环境层</div>
                <div class="layer-modules">
                    <div class="module-card">
                        <div class="module-title">飞行动力学仿真</div>
                        <ul class="module-content">
                            <li>6DOF模型</li>
                            <li>气动模型</li>
                            <li>环境模型</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">传感器仿真</div>
                        <ul class="module-content">
                            <li>GPS信号</li>
                            <li>IMU数据</li>
                            <li>视觉数据</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">威胁环境仿真</div>
                        <ul class="module-content">
                            <li>GPS欺骗</li>
                            <li>网络攻击</li>
                            <li>数据劫持</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 监控分析层 -->
            <div class="architecture-layer monitoring-layer" id="monitoringLayer">
                <div class="layer-title">监控分析层</div>
                <div class="layer-modules">
                    <div class="module-card">
                        <div class="module-title">性能监控</div>
                        <ul class="module-content">
                            <li>实时指标</li>
                            <li>系统状态</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">安全事件分析</div>
                        <ul class="module-content">
                            <li>威胁检测</li>
                            <li>响应时间</li>
                        </ul>
                    </div>
                    <div class="module-card">
                        <div class="module-title">数据记录回放</div>
                        <ul class="module-content">
                            <li>测试数据</li>
                            <li>事件日志</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试能力 -->
        <div class="capabilities-section">
            <div class="capabilities-title">测试能力</div>
            <div class="capabilities-grid">
                <div class="capability-item">
                    <div class="capability-label">
                        <span class="capability-icon">⚡</span>
                        实时HIL仿真
                    </div>
                    <div class="capability-value">1KHz飞控回路 + 100Hz导航回路</div>
                    <div class="capability-description">高频率实时仿真确保测试精度</div>
                </div>

                <div class="capability-item">
                    <div class="capability-label">
                        <span class="capability-icon">🎯</span>
                        威胁注入
                    </div>
                    <div class="capability-value">GPS欺骗、数据劫持、网络攻击</div>
                    <div class="capability-description">全方位威胁场景模拟验证</div>
                </div>

                <div class="capability-item">
                    <div class="capability-label">
                        <span class="capability-icon">📊</span>
                        性能监控
                    </div>
                    <div class="capability-value">延迟&lt;1ms、吞吐量&gt;1Gbps</div>
                    <div class="capability-description">实时性能指标监控分析</div>
                </div>

                <div class="capability-item">
                    <div class="capability-label">
                        <span class="capability-icon">🔄</span>
                        长期测试
                    </div>
                    <div class="capability-value">连续运行&gt;100小时稳定性验证</div>
                    <div class="capability-description">长时间稳定性和可靠性验证</div>
                </div>
            </div>
        </div>

        <!-- 平台特色功能 -->
        <div class="features-section">
            <div class="features-title">
                <span>🚀</span>
                平台特色功能
            </div>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-title">真实硬件集成</div>
                    <div class="feature-content">
                        集成与"翼龙-2"或"彩虹-4"同系列的真实飞控计算机和任务计算机硬件，确保测试环境的真实性和可信度。
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-title">典型战术背景仿真</div>
                    <div class="feature-content">
                        支持低空突防、高空侦察、编队协同等多种战术场景仿真，验证复杂环境下的系统性能。
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-title">极端环境模拟</div>
                    <div class="feature-content">
                        模拟-40°C~+85°C温度循环、31000g过载冲击、多重威胁并发等极端测试条件。
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-title">科学基线测试</div>
                    <div class="feature-content">
                        建立标准化测试环境和传统方案基线，确保对比测试的公平性和可重复性。
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-title">多重威胁并发</div>
                    <div class="feature-content">
                        同时模拟GPS欺骗+数据链干扰+电磁压制的复合威胁环境，验证系统综合防护能力。
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-title">实时监控分析</div>
                    <div class="feature-content">
                        实时监控系统性能指标、安全事件和通信质量，提供全面的测试数据分析和回放功能。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('layersContainer');
            const svg = document.getElementById('connectorCanvas');

            // 层间连接关系
            const connections = [
                // 硬件层到仿真层的双向连接
                { from: 'hardwareLayer', to: 'simulationLayer', type: 'bidirectional' },
                // 仿真层到监控层的双向连接
                { from: 'simulationLayer', to: 'monitoringLayer', type: 'bidirectional' }
            ];
            
            function getLayerPosition(layerId, containerRect) {
                const layer = document.getElementById(layerId);
                const layerRect = layer.getBoundingClientRect();
                const relX = layerRect.left - containerRect.left;
                const relY = layerRect.top - containerRect.top;
                
                return {
                    centerX: relX + layerRect.width / 2,
                    bottom: relY + layerRect.height,
                    top: relY,
                    width: layerRect.width
                };
            }
            
            function drawBidirectionalConnection(layer1Pos, layer2Pos) {
                const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                
                // 向下箭头
                const downArrow = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                downArrow.setAttribute('x1', layer1Pos.centerX - 20);
                downArrow.setAttribute('y1', layer1Pos.bottom + 10);
                downArrow.setAttribute('x2', layer2Pos.centerX - 20);
                downArrow.setAttribute('y2', layer2Pos.top - 10);
                downArrow.setAttribute('stroke', 'var(--color-connection)');
                downArrow.setAttribute('stroke-width', '3');
                downArrow.setAttribute('marker-end', 'url(#arrowhead)');
                downArrow.classList.add('connection-line');
                
                // 向上箭头
                const upArrow = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                upArrow.setAttribute('x1', layer2Pos.centerX + 20);
                upArrow.setAttribute('y1', layer2Pos.top - 10);
                upArrow.setAttribute('x2', layer1Pos.centerX + 20);
                upArrow.setAttribute('y2', layer1Pos.bottom + 10);
                upArrow.setAttribute('stroke', 'var(--color-connection)');
                upArrow.setAttribute('stroke-width', '3');
                upArrow.setAttribute('marker-end', 'url(#arrowhead-reverse)');
                upArrow.classList.add('connection-line');
                
                group.appendChild(downArrow);
                group.appendChild(upArrow);
                svg.appendChild(group);
            }

            function drawConnections() {
                // 清空旧连接线，保留defs
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                const containerRect = container.getBoundingClientRect();
                
                connections.forEach(conn => {
                    const layer1Pos = getLayerPosition(conn.from, containerRect);
                    const layer2Pos = getLayerPosition(conn.to, containerRect);
                    
                    if (conn.type === 'bidirectional') {
                        drawBidirectionalConnection(layer1Pos, layer2Pos);
                    }
                });
            }

            // 添加交互效果
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                card.addEventListener('click', () => {
                    const title = card.querySelector('.module-title').textContent;
                    const subtitle = card.querySelector('.module-subtitle')?.textContent || '';
                    const items = Array.from(card.querySelectorAll('.module-content li')).map(li => li.textContent);
                    
                    console.log(`${title} ${subtitle}:`, items);
                    // 可以添加更多交互功能，如显示详细说明
                });
            });

            const observer = new ResizeObserver(drawConnections);
            window.addEventListener('load', () => {
                setTimeout(drawConnections, 300); // 延迟确保动画完成
                observer.observe(container);
            });
        });
    </script>
</body>
</html>