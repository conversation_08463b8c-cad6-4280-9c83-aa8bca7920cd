<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图1-1 "赛安"异构安全协处理核心原理架构图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的实现
         * 核心设计理念：CSS变量体系化 + Flexbox分层布局 + SVG动态连接线
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-primary: #1e40af; /* 主色调-蓝色 */
            --color-secondary: #dc2626; /* 安全模组-红色 */
            --color-accent: #059669; /* 协同连接-绿色 */
            --color-warning: #ea580c; /* 威胁警告-橙色 */
            --color-core: #7c3aed; /* 核心模块-紫色 */
            
            --color-border-light: #e5e7eb;
            --color-border-security: #dc2626;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-bg-security: #fef2f2;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;
            --color-text-warning: #ea580c;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --shadow-security: 0 4px 8px rgba(220, 38, 38, 0.15);
            --border-radius: 8px;
            --line-color: #6b7280;
            --line-color-warning: #ea580c;
            --line-color-security: #dc2626;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .flowchart-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
        }

        .flowchart-main-container {
            position: relative;
            width: 1200px;
            height: auto;
            background-color: var(--color-bg-main);
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .layer {
            display: flex;
            align-items: stretch;
            gap: 25px;
        }
        
        .top-layer { justify-content: space-around; }
        .security-layer { justify-content: center; }
        .core-modules-layer { justify-content: space-around; }
        .interface-layer { justify-content: center; }
        .summary-layer { justify-content: center; }

        .flow-stage {
            background-color: var(--color-bg-main);
            border: 2px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 16px 20px;
            box-shadow: var(--shadow-subtle);
            display: flex;
            flex-direction: column;
            text-align: center;
            position: relative;
        }

        /* 特殊样式 */
        .security-processor {
            border: 3px double var(--color-border-security);
            background-color: var(--color-bg-security);
            box-shadow: var(--shadow-security);
            width: 800px;
            padding: 25px;
        }

        .core-module {
            border-color: var(--color-core);
            width: 180px;
        }

        .threat-indicator {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--color-warning);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .stage-title {
            font-family: var(--font-heading);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.8rem;
            color: var(--color-text-dark);
        }

        .stage-content-list {
            list-style: none;
            font-size: 0.85rem;
            line-height: 1.4;
            color: var(--color-text-light);
            text-align: left;
            padding-left: 16px;
        }
        
        .stage-content-list li {
            position: relative;
            margin-bottom: 3px;
        }

        .stage-content-list li::before {
            content: "•";
            position: absolute;
            left: -12px;
            color: var(--color-accent);
        }

        .core-modules-container {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            margin: 20px 0;
        }

        .collaboration-indicator {
            text-align: center;
            margin: 15px 0;
            font-size: 0.9rem;
            color: var(--color-accent);
            font-weight: 500;
        }

        .interface-details {
            display: flex;
            justify-content: space-around;
            gap: 20px;
            margin-top: 10px;
        }

        .interface-item {
            text-align: center;
            font-size: 0.8rem;
        }

        .interface-name {
            font-weight: 600;
            color: var(--color-primary);
        }

        .interface-spec {
            color: var(--color-text-light);
            font-size: 0.75rem;
        }

        .protection-effects {
            background-color: var(--color-bg-security);
            border: 2px solid var(--color-secondary);
            padding: 15px;
            border-radius: var(--border-radius);
            margin-top: 15px;
        }

        .effects-title {
            font-weight: 600;
            color: var(--color-secondary);
            margin-bottom: 8px;
        }

        .effects-content {
            font-size: 0.9rem;
            color: var(--color-text-dark);
        }

        .advantages-summary {
            background-color: var(--color-bg-light);
            border: 2px solid var(--color-primary);
            padding: 20px;
            border-radius: var(--border-radius);
            width: 100%;
        }

        .advantages-title {
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: 12px;
            font-size: 1.1rem;
        }

        .advantages-list {
            list-style: none;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .advantages-list li {
            position: relative;
            padding-left: 20px;
            font-size: 0.9rem;
            color: var(--color-text-dark);
        }

        .advantages-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--color-accent);
            font-weight: bold;
        }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        /* 响应式设计 */
        @media (max-width: 1280px) {
            .flowchart-main-container {
                width: 95%;
                max-width: 1200px;
            }
        }
    </style>
</head>
<body>
    <div class="flowchart-title">图1-1 "赛安"异构安全协处理核心原理架构图</div>
    
    <div class="flowchart-main-container" id="myFlowchartContainer">
        <svg class="connector-canvas" id="connectorCanvas">
            <defs>
                <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--line-color)" />
                </marker>
                <marker id="arrowhead-warning" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--line-color-warning)" />
                </marker>
                <marker id="arrowhead-security" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4"
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--line-color-security)" />
                </marker>
            </defs>
        </svg>

        <!-- 顶层：无人机主航电系统 -->
        <div class="layer top-layer">
            <div class="flow-stage" id="flightControlComputer">
                <div class="stage-title">主飞控计算机<br>(FCC)</div>
                <ul class="stage-content-list">
                    <li>飞行控制</li>
                    <li>姿态稳定</li>
                    <li>1KHz实时回路</li>
                </ul>
                <div class="threat-indicator">威胁：指令篡改</div>
            </div>
            <div class="flow-stage" id="missionComputer">
                <div class="stage-title">任务计算机<br>(MC)</div>
                <ul class="stage-content-list">
                    <li>任务规划</li>
                    <li>载荷控制</li>
                    <li>目标识别</li>
                </ul>
                <div class="threat-indicator">威胁：数据窃取</div>
            </div>
            <div class="flow-stage" id="communicationModule">
                <div class="stage-title">通信模块<br>(CM)</div>
                <ul class="stage-content-list">
                    <li>数据链通信</li>
                    <li>协议处理</li>
                    <li>信号调制</li>
                </ul>
                <div class="threat-indicator">威胁：链路劫持</div>
            </div>
        </div>

        <!-- 安全协处理器层 -->
        <div class="layer security-layer">
            <div class="flow-stage security-processor" id="securityProcessor">
                <div class="stage-title">"赛安"安全协处理器 (独立安全域)</div>
                
                <div class="stage-title" style="margin-top: 20px; font-size: 1rem;">核心安全原理</div>
                
                <div class="core-modules-container">
                    <div class="flow-stage core-module" id="rtTee">
                        <div class="stage-title" style="font-size: 0.95rem;">RT-TEE可信环境</div>
                        <ul class="stage-content-list">
                            <li>硬件隔离</li>
                            <li>&lt;1ms切换</li>
                            <li>实时调度</li>
                        </ul>
                    </div>
                    <div class="flow-stage core-module" id="cryptoAccelerator">
                        <div class="stage-title" style="font-size: 0.95rem;">国密加速器</div>
                        <ul class="stage-content-list">
                            <li>SM2/3/4</li>
                            <li>2Gbps加速</li>
                            <li>自主可控</li>
                        </ul>
                    </div>
                    <div class="flow-stage core-module" id="zeroTrustEngine">
                        <div class="stage-title" style="font-size: 0.95rem;">零信任引擎</div>
                        <ul class="stage-content-list">
                            <li>5W模型</li>
                            <li>动态控制</li>
                            <li>多因子认证</li>
                        </ul>
                    </div>
                    <div class="flow-stage core-module" id="aiThreatDetection">
                        <div class="stage-title" style="font-size: 0.95rem;">AI威胁检测</div>
                        <ul class="stage-content-list">
                            <li>LSTM+GNN</li>
                            <li>&lt;20ms推理</li>
                            <li>&gt;90%准确率</li>
                        </ul>
                    </div>
                </div>

                <div class="collaboration-indicator">
                    ↕ 安全策略协同 ↕
                </div>

                <div class="flow-stage" id="unifiedInterface" style="margin-top: 15px;">
                    <div class="stage-title">统一安全接口层</div>
                    <div class="interface-details">
                        <div class="interface-item">
                            <div class="interface-name">PCIe 3.0</div>
                            <div class="interface-spec">(8Gbps)</div>
                        </div>
                        <div class="interface-item">
                            <div class="interface-name">CAN 2.0</div>
                            <div class="interface-spec">(1Mbps)</div>
                        </div>
                        <div class="interface-item">
                            <div class="interface-name">Ethernet</div>
                            <div class="interface-spec">(1Gbps)</div>
                        </div>
                        <div class="interface-item">
                            <div class="interface-name">UART</div>
                            <div class="interface-spec">(调试)</div>
                        </div>
                    </div>
                </div>

                <div class="protection-effects">
                    <div class="effects-title">防护效果：</div>
                    <div class="effects-content">指令防篡改 + 数据防窃取 + 链路防劫持 + 威胁主动检测</div>
                </div>
            </div>
        </div>

        <!-- 核心优势总结 -->
        <div class="layer summary-layer">
            <div class="advantages-summary">
                <div class="advantages-title">核心原理优势：</div>
                <ul class="advantages-list">
                    <li>异构分离：安全与性能完全解耦，互不影响</li>
                    <li>硬件信任根：从芯片级构建信任链，不可绕过</li>
                    <li>零信任架构：持续验证，动态防护，内外兼防</li>
                    <li>自主可控：完全国产化，摆脱技术依赖</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('myFlowchartContainer');
            const svg = document.getElementById('connectorCanvas');

            const connections = [
                // 主航电系统到安全协处理器的威胁连接
                { from: 'flightControlComputer', to: 'securityProcessor', fromPort: 'bottom', toPort: 'top', type: 'warning' },
                { from: 'missionComputer', to: 'securityProcessor', fromPort: 'bottom', toPort: 'top', type: 'warning' },
                { from: 'communicationModule', to: 'securityProcessor', fromPort: 'bottom', toPort: 'top', type: 'warning' },
                
                // 核心模块之间的协同连接
                { from: 'rtTee', to: 'cryptoAccelerator', fromPort: 'right', toPort: 'left', type: 'collaboration' },
                { from: 'cryptoAccelerator', to: 'zeroTrustEngine', fromPort: 'right', toPort: 'left', type: 'collaboration' },
                { from: 'zeroTrustEngine', to: 'aiThreatDetection', fromPort: 'right', toPort: 'left', type: 'collaboration' },
            ];
            
            function getPortPosition(element, portSide, containerRect) {
                const elemRect = element.getBoundingClientRect();
                const relX = elemRect.left - containerRect.left;
                const relY = elemRect.top - containerRect.top;
                
                switch(portSide) {
                    case 'top': return { x: relX + elemRect.width / 2, y: relY };
                    case 'bottom': return { x: relX + elemRect.width / 2, y: relY + elemRect.height };
                    case 'left': return { x: relX, y: relY + elemRect.height / 2 };
                    case 'right': return { x: relX + elemRect.width, y: relY + elemRect.height / 2 };
                    default: return { x: relX + elemRect.width / 2, y: relY + elemRect.height / 2 };
                }
            }
            
            function drawConnection(p1, p2, type = 'normal') {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                
                let pathData;
                if (type === 'collaboration') {
                    // 直线连接用于协同关系
                    pathData = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;
                } else {
                    // 弯曲连接用于其他关系
                    const midY = p1.y + (p2.y - p1.y) / 2;
                    pathData = `M ${p1.x} ${p1.y} V ${midY} H ${p2.x} V ${p2.y}`;
                }

                path.setAttribute('d', pathData);
                path.setAttribute('fill', 'none');
                path.setAttribute('stroke-width', type === 'collaboration' ? '2' : '1.5');
                
                if (type === 'warning') {
                    path.setAttribute('stroke', 'var(--line-color-warning)');
                    path.setAttribute('stroke-dasharray', '5,5');
                    path.setAttribute('marker-end', 'url(#arrowhead-warning)');
                } else if (type === 'collaboration') {
                    path.setAttribute('stroke', 'var(--color-accent)');
                    path.setAttribute('marker-end', 'url(#arrowhead)');
                } else {
                    path.setAttribute('stroke', 'var(--line-color)');
                    path.setAttribute('marker-end', 'url(#arrowhead)');
                }
                
                svg.appendChild(path);
            }

            function drawConnections() {
                // 清空旧连接线，保留defs
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                const containerRect = container.getBoundingClientRect();
                
                connections.forEach(conn => {
                    const fromEl = document.getElementById(conn.from);
                    const toEl = document.getElementById(conn.to);
                    if (!fromEl || !toEl) return;
                    
                    const p1 = getPortPosition(fromEl, conn.fromPort, containerRect);
                    const p2 = getPortPosition(toEl, conn.toPort, containerRect);
                    drawConnection(p1, p2, conn.type);
                });
            }

            const observer = new ResizeObserver(drawConnections);
            window.addEventListener('load', () => {
                setTimeout(drawConnections, 100); // 延迟确保布局完成
                observer.observe(container);
            });
        });
    </script>
</body>
</html>