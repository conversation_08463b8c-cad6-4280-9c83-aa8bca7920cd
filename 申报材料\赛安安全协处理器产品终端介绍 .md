# “赛安”安全协处理器：构建信息系统真安全

## ——基于零信任的嵌入式智能终端安全体系建设

**主讲单位：** 西交网络空间安全研究院（Cyber Science Research Academy of Xijiao）

## 一、单位及研发团队介绍

### （一）单位简介

西交网络空间安全研究院是由西安交通大学与浙江省诸暨市人民政府合作组建的研究机构，由管晓宏院士领导，致力于开展网络安全自主可控专用芯片、软硬结合内外网安全管控系统以及行业安全管控整体解决方案等信息安全核心技术的研究。

研究院以“科学研究、成果转化、人才引育、产业孵化”为四大定位与方向，秉持“立足浙江、安芯引领、放眼全球、产业赋能”的战略目标，立足浙江、辐射全国，充分发挥西安交通大学科研人才优势和本地产业经济优势，打造集多项功能于一体的研发机构，为全面推进数字化改革保驾护航，建设全国网络与数据安全领域具有重要影响力的科创高地和产业聚集区。

### （二）研发团队

- **管晓宏 院士**：中国科学院院士、IEEE Fellow，担任西安交通大学电子与信息学部主任、教授以及西交网络空间安全研究院院长。
- **王维平 常务副院长**：信息缺失（原文为XX）。
- **李卫 副院长**：西南交通大学教授，研究网络安全、网络管理等领域。
- **韩博 副院长**：西安交通大学计算机研究员，曾任西安市大数据资源管理局原副局长。
- **赵豫平 院长助理**：创办西安文渊软件科技有限公司、陕西诚泰科技讯息有限责任公司等多家企业。
- **梁梦雷 芯片主任工程师**：北京邮电大学硕士，参与创办码灵半导体，主导研发多款MPU芯片。

## 二、目录

- **Part 1: 信息安全现况**：总结、描述嵌入式智能终端的基本情况，以及嵌入式智能终端在信息安全下的新形势。
- **Part 2: 存在的问题**：分析嵌入式智能终端遇到的安全问题，总结现有主流解决方案及其优缺点。
- **Part 3: “赛安”解决方案**：介绍“赛安”安全协处理器的设计思路、整体框架、主要功能、参数和性能优势。
- **Part 4: “赛安”应用场景**：总结归纳了“赛安”安全协处理器的移动办公、开放场所交互终端等三个典型应用场景。

## 三、Part 1: 信息安全现况

### （一）1.1 嵌入式智能终端概况

嵌入式智能终端是集成了计算机技术和通信技术的智能设备，具有数据采集、处理、存储和传输等功能，配备通用的高性能处理器（SOC）和存储器，以及专用的控制和通信电路、传感器和执行器等，在各个领域得到广泛应用。

**典型嵌入式智能终端**：手机、PDA、机顶盒、物联网网关、平板电脑、智能电视、信息引导终端等。

**典型应用场景**：智慧政务、智慧警务、移动办公、智能家居、智慧商场、工业智能化等。

### （二）1.2 在数字化浪潮中，智能终端已经成为新的边界

在数字化转型浪潮中，云计算、大数据和物联网等技术的业务应用正迅速落地，大多数企事业单位采用公有云业务、私有云业务以及传统网络的混合模式网络解决方案，这种混合模式打破了传统的网络边界，使得智能终端成为新的安全关注点。

- **网络边界变化**：大量业务上云导致网络边界模糊，云业务和远程办公使网络边界范围大量扩展；传统本地防护模式无法应对多业务和远程需求；基于VPN的外网访问授权对进入内网的操作不可控。
- **终端的安全角色**：终端既是首要防线，亦是最终防线，远程办公VPN使终端成为安全的第一道防线，云上业务使终端成为安全的最后一道防线，因此终端安全更加重要，需关注终端设备自身环境安全以及访问业务的人是否经过授权、是否合理使用。

### （三）1.3 在激烈的攻防下，信息安全事件频繁发生

随着信息技术的快速发展，嵌入式智能终端的信息安全问题愈发引人关注，面对各类威胁攻击，传统防护手段逐渐失效，近年来，一些企事业单位遭受各类攻击，面临重大损失。

**攻击类型**：内部恶意行为、商业竞争、政治目的、黑客的恶意报复、灰产的利益纠纷（诈骗、勒索、信息贩卖）等。

**典型安全事件**：

- “俄乌冲突”中，双方的政府、金融机构、企业等关键信息基础设施均遭到了猛烈的攻击，“网络战”成为俄乌冲突的第二战场。
- 浙江某公司机顶盒遭到入侵，将大量反动宣传图文传播到了某市15.98万户，一共46.5万台电视机顶盒中，出现重大安全事故。
- 武汉某公司智能终端受到通信协议伪造、破坏固件等攻击，造成100余台设备无法使用、10万台设备离线，遭受重大经济损失。
- 在“白帽”黑客会议上，巴纳比·杰克演示了在10~15米范围内入侵一台心脏起搏器，让机器释放出830伏的电压。

### （四）1.4 实战攻防演练，成为业界共识

随着秉承“实践是检验真理的唯一标准”普遍共识的《数据安全法》和《个人信息保护法》等法律法规落地实施，网络安全已上升为国家战略的重要组成部分。

攻防博弈对抗是网络安全的本质，企业与监管机构已从“产品检查”转向“能力检查”，传统的堆砌设备方式不再满足合规和防御要求。

**相关法律法规与标准**：

| 时间   | 法律名称           | 要求                                                                                                           |
| ------ | ------------------ | -------------------------------------------------------------------------------------------------------------- |
| 2021年 | 《数据安全法》     | 旨在保障数据安全，促进数据开发利用，保护个人、组织的合法权益，维护国家主权、安全和发展利益。                   |
| 2021年 | 《个人信息保护法》 | 旨在保护个人信息权益，规范个人信息处理活动，促进个人信息合理利用。                                             |
| 2020年 | 《密码法》         | 旨在规范密码的应用和管理，促进密码事业发展，保障网络与信息安全。                                               |
| 2017年 | 《网络安全法》     | 旨在维护网络空间主权和国家安全、社会公共利益，保护公民、法人和其他组织的合法权益，促进经济社会信息化健康发展。 |

**相关标准**：

- GB 17859-1999：计算机信息系统安全保护等级划分准则
- GB/T 22240-2008：信息安全技术信息系统安全等级保护定级指南
- GB/T 22239-2019：信息安全技术—网络安全等级保护基本要求
- GB/T 18336-2015：信息安全信息技术安全评估准则
- GM/T 0003-2012：随机性检测规范
- GM/T 0004-2012：密码术语
- GM/T 0054-2018：SM9标识密码算法使用规范

## 四、Part 2: 存在的问题

### （一）2.1 嵌入式智能终端面临的主要安全问题

- **固件攻击**：利用固件漏洞或者篡改固件，对系统进行攻击，获取系统控制权。
- **通信安全攻击**：在与其他设备进行通信时，进行中间人、重放等攻击，导致数据被窃听或篡改。
- **非法访问**：通过非法手段获取系统的访问权限，进行非法操作，窃取敏感信息或破坏系统功能。
- **恶意软件感染**：木马或病毒恶意感染操作系统，对系统功能和数据进行破坏或者窃取。
- **越权操作**：用户向其他人泄露了操作密码或登录方式，非授权人进行越权操作，窃取敏感信息或进行非授权操作。
- **物理攻击和供应链攻击**：拆解设备、篡改硬件、植入恶意代码等攻击，获取敏感信息或破坏系统功能。

### （二）2.2 当前主流的安全解决方案

目前主流的嵌入式智能终端安全解决方案以软件为主、硬件为辅。

| 类别 | 名称                    | 描述                                                                   | 主流产品和应用                                                |
| ---- | ----------------------- | ---------------------------------------------------------------------- | ------------------------------------------------------------- |
| 软件 | 安全操作系统            | 采用权限管理、访问控制等机制，对用户访问终端设备的权限进行分级分类管理 | Android、HarmonyOS、Kali Linux、银河麒麟、SELinux、TrustedBSD |
| 软件 | 系统安全类软件          | 进行病毒查杀、骚扰拦截、软件权限管理、手机防盗等安全防护               | 360手机助手、腾讯手机管家、华为手机管家、GO手机助手           |
| 硬件 | 生物识别                | 通过采集人员声纹、指纹、人脸等生物特征，进行身份认证                   | 亚略特、汇顶科技、思立微、科大讯飞、旷视科技                  |
| 硬件 | 安全元素 Secure Element | 集成加密算法和安全协议的芯片，提供硬件级别数据保护和身份认证           | NXP T系列、瑞芯微 RK系列、全志 T系列、TIJ系列                 |
| 硬件 | 可信执行环境 TEE        | 允许敏感操作在受保护的硬件环境中执行，以防止数据被篡改或窃取           | ARM TrustZone、Intel SGX、AMD SEV、TEE-BOX、OpenPort          |
| 硬件 | 可信平台模块 TPM        | 提供加解密、签名等安全操作在独立硬件模块处理，保护数据完整性和机密性   | IntelTXT、AMD PSP、ARM TrustZone Crypto I/O                   |

### （三）2.3 当前主流安全解决方案存在的问题

- **软件可绕过**：可通过刷机、恶意入侵等方式获取终端超级权限，绕过所有软件层面限制。
- **数据保密性差**：安全存储和加密、解密能力不足，部分关键数据明文存储、传输。
- **没有安全审计**：没有实时监控、定期检查的在线安全审计机制，难以及时发现安全事件，同时追溯和定责。
- **身份认证机制单一**：使用指纹、人脸、短信验证、许可设备等单一方式进行身份认证，可绕过或者伪造。
- **安全方面计算能力不足**：安全模块的加解密、通讯管理、运算能力不足，放弃了部分限制。
- **兼容性和扩展性不足**：跨平台、跨系统集成难度大，增加安全功能困难。

## 五、Part 3: “赛安”解决方案

### （一）3.1 研发思路

在强监管的背景下，以零信任为基础、以多维度为枢纽，采用动态化策略构建以“赛安”安全协处理器为核心的嵌入式智能终端安全体系。

- **强监管**：在特殊场景，制定和执行严格的政策和标准，对嵌入式智能终端及其相关活动进行严格的监督和管理，以确保其安全性、合规性和稳定性。
- **零信任**：将任何人、任何操作、任何设备视为不安全范畴；从“不信任、验证一切”出发，构建安全体系，预防信任危机。
- **多维度**：从用户/网络/数据/应用等多维度构建安全体系；从操作实体的5W1H等维度构建安全体系。
- **动态化**：将系统漏洞、产品漏洞、管理漏洞、威胁手段使用自动化工具和人工智能来进行实时威胁检测、自适应访问控制等，建立安全动态网络观。

### （二）3.2 整体架构

“赛安”协处理器作为旁路管控和安全管控的核心，与主CPU、操作系统、外部器件和服务器协同工作。

- **服务器端**：包含安全策略管理、远程监控、专有数据传输协议、远程管理、安全审计、通信加密、防火墙与网络安全、日志记录等。
- **操作系统端**：与服务器通过加密通信和专有协议连接，包含访问控制、应用、漏洞与补丁管理、恶意软件防护、日志记录等。
- **“赛安”协处理器**
  - **安全内核**：受信任的应用程序、安全实时时钟、真随机数发生器、不可写区域、密码运算引擎、安全引导、信任区、监视器、安全系统等。
  - **其他区域**：需要安全系统支持的应用程序、显示屏和摄像头控制、视频/音频处理程序、与安全操作系统的API、电源管理等。
- **外部器件**：通过旁路和安全管控与协处理器连接，如北斗、4G/5G、WiFi/蓝牙、LAN、USB、指纹识别、摄像头、电池等。

### （三）3.3 主要功能

1. **多因素身份认证和授权管理**：支持基于位置、指纹、摄像头等多种硬件的身份认证和授权管理功能，以确保只有经过授权的用户才能访问和使用设备。
2. **加密和解密运算**：内置高性能加密和解密运算模块，支持对称、非对称、杂凑等加密算法和数据加解密操作，保证数据传输和存储的安全性。
3. **安全启动和完整性校验**：基于芯片的不可写区域，实现设备固件具有无法更改、不可绕过等，以确保系统在启动时不会受到攻击和篡改。
4. **数据安全存储**：提供硬件级别的数据安全存储功能，保证存储在芯片内部的数据不会被非法访问、暴露和窃取。
5. **数据安全传输**：自主开发专用数据传输协议，控制数据传输的安全性和性能，满足业务需求和安全标准，确保数据传输的完整性和机密性。
6. **安全策略**：支持可配置的安全策略，以根据不同的应用场景和安全需求进行定制和调整。
7. **安全审计**：支持实时安全监测功能，以实时监测设备的运行状态和安全状况，及时发现和处理安全威胁。

### （四）3.4 主要性能（赛安二号）

| 项目           | 参数                                                                                            |
| -------------- | ----------------------------------------------------------------------------------------------- |
| 内核           | ARM Cortex A53 * 4                                                                              |
| 主频           | 1.6GHz                                                                                          |
| 多媒体功能     | 支持H.265/VP6/H.264视频解码、FHD 1920x1200@60fps显示输出                                        |
| 工作温度       | 工业级: -40℃ ~ 85℃                                                                            |
| 支持算法       | 支持中国商业密码算法SM1/SM2/SM3/SM4/SM7；支持RSA2048/3072、ECC256/384、SHA1/256/384、AES128/256 |
| 通信接口       | 2*GE、2*CAN、PCIE/USB3.0、2*USB2.0、10*UART、30*PWM、4*SPI、9*I2C                         |
| 封装方式和尺寸 | LFBGA-346 17mm*17mm                                                                             |
| 支持操作系统   | linux、VxWorks、HarmonyOS                                                                       |
| 认证报告       | CE、ROHS、100%国产物料认证                                                                      |

### （五）3.5 主要特点

1. **硬件级安全保障**：利用协处理器芯片的不可写区域，实现根认证、设备固件的防篡改和防绕过等，确保系统在启动时的安全性；提供硬件级别的数据安全存储，保护存储在芯片内部的数据不被非法访问、暴露或窃取。
2. **零信任的认证和授权**：贯彻“不信任来自任何人、任何设备的任何操作，验证一切”的原则；基于多种硬件进行身份认证，确保合法用户访问设备；提供细粒度的授权管理功能，允许对不同的用户和应用赋予不同的权限。
3. **高效加解密运算**：内置的加密解密模块支持多种算法，能够在数据存储和传输时，快速、高效地处理大量数据；自主开发了专用协议，针对安全需求进行了优化，保障数据传输过程中的完整性和机密性。
4. **实时安全监测与响应**：具备实时安全监测功能，能够实时监测设备的运行状态和安全状况；发现安全威胁或异常行为时，能迅速作出响应，采取必要的措施来保护系统的安全性。

### （六）3.6 产品优势

- **全面**：提供国产化的一站式安全解决方案；“赛安”全产业链国产化，自主可控；集成多种安全功能，无需额外配置多个组件或软件；提供从数据传输、处理到存储的端到端安全保障；兼容多种应用场景。
- **灵活**：灵活应变，安全随行；根据实时威胁和系统状态动态调整安全策略，确保始终能够应对新出现的安全风险；允许用户根据实际需求定制特定的安全服务和功能；作为协处理器，可以与现有系统集成，并根据未来需求进行扩展和升级。
- **高效**：高效守护，安全无忧；内置专用加密解密引擎，实现硬件级别的高速加密解密处理；并行响应各种安全请求，低延迟执行启动检查、认证等操作；合理的负载均衡，智能的资源分配和管理硬件资源，最大程度地提升系统的整体性能和数据传输效率。

## 五、Part 4: “赛安”应用场景

### （一）应用场景一：移动办公

- **终端产品形态**：安全平板、手机、PDA（可带红外/扫描/FD/ARC/身份证识别）。
- **特点**：CPU+赛安安全协处理器双处理器架构；双电池模式，确保赛安协处理器独立工作；基于Who/When/Where/Which/What五位一体的身份认证；基于国密算法的专有通信协议，通信全链路加密；数据存储、传输和处理全过程加密；进行使用审计（截屏、键盘记录等），且审计内容无法被篡改。
- **应用行业**：政务大厅、企业、医院、银行、法院、公安等。

### （二）应用场景二：开放场所交互终端

- **终端产品形态**：收银终端、引导屏、叫号机、智能柜员机、工控机。
- **特点**：赛安安全处理器，不需要额外CPU；支持linux、Vxworks、HarmonyOS系统；基于Who/When/Where/Which/What五位一体的身份认证；基于国密算法的专有通信协议，通信全链路加密；数据存储、传输和处理全过程加密；进行使用审计（截屏、键盘记录等），且审计内容无法被篡改。
- **应用场景**：政务大厅、医院、银行、商店、商场、工业等。

### （三）应用场景三：多媒体播放控制终端

- **终端产品形态**：电视机盒子、智能音响、车载娱乐、大屏控制器。
- **特点**：赛安安全处理器，不需要额外CPU；支持linux、Vxworks、HarmonyOS系统；基于Who/When/Where/Which/What五位一体的身份认证；基于国密算法的专有通信协议，通信全链路加密；数据存储、传输和处理全过程加密；进行使用审计（截屏、键盘记录等），且审计内容无法被篡改。
- **应用场景**：家庭音影、车辆、各类信息发布系统等。

## 六、结束页

**THANKS**
**西交网络空间安全研究院**
**Cyber Science Research Academy of Xijiao**
