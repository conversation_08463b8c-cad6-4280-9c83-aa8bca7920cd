# 西交网络空间安全研究院简介 平台 20250619.pdf

## 第1页：封面

*图示：页面左上角为“西交网络空间安全研究院”相关标识，整体设计简洁，突出主题。*

**西交网络空间安全研究院**
XIAOITDTECRCV（标识相关）

# 安全管控技术体系

## 构建网络空间安全新形态

**2025年5月25日**

---

## 第2页：目录

*图示：页面左侧有由键盘与代码、地球网络等元素组成的视觉元素，中间蓝色方框标注“目录 CONCENTS”，整体布局清晰。*

**西交网络空间安全研究**XUAOAO 2025

- **研究院简介**
- **网络安全行业思考**
- **技术成果进展**
- **定位与服务**

---

## 第3页：研究院创立

### 研究院创立

*图示：页面包含两张记录研究院创立重要时刻的照片，左图为签约仪式现场，右图为揭牌场景。*

**左图描述**：签约仪式现场，多位领导嘉宾在座，背景屏幕显示相关签约信息。

> 2023年5月诸暨市人民政府与西安交通大学签合作协议在诸暨建设西交网络空间安全研究院

**右图描述**：西安交通大学常务副校长别朝红与诸暨市市委副书记、市长张昆仑共同为研究院牌匾揭牌。

> 2023年7月西安交通大学常务副校长别朝红，诸暨市市委副书记、市长张昆仑共同为研究院揭牌

**诸暨市人民政府与西安交通大学大力支持**

---

## 第4页：研究院院长、首席科学家

### 研究院院长、首席科学家

*图示：左侧为管晓宏院士照片，右侧为其身份及成就介绍，布局合理。*

**管晓宏院士**

- 担任研究院院长、首席科学家，在网络空间安全领域有深厚造诣
- 带领团队取得多项重大科研成果，获多项国家级奖项及荣誉

---

## 第5页：智能网络与网络安全教育部重点实验室研究成果

### 智能网络与网络安全教育部重点实验室研究成果

*图示：下方展示多张获奖证书扫描件，包括国家自然科学奖、科技进步奖等，直观呈现成果荣誉。*

| 类别       | 主要成果                                                                               |
| ---------- | -------------------------------------------------------------------------------------- |
| 科技奖励   | 国家三大奖8项，国家级教学奖6项，含2018年国家自然科学二等奖、2017年国家科技进步二等奖等 |
| 科研项目   | 国家重点研发、自然基金创新群体等项目，总到款超10亿元，成果转化超1.6亿元                |
| 论文、专著 | 期刊论文1800余篇，ESI高被引论文200余篇，国际专著15部，国内专著33部                     |
| 发明专利   | 授权发明专利1700余项，牵头制定标准26项                                                 |

---

## 第6页：与华为公司产品线和实验室深度合作取得重大进展

### 与华为公司产品线和实验室深度合作取得重大进展

*图示：包含管晓宏院士团队与华为高层合影、管晓宏院士在华为系统工程大会发言、会议室场景及颁奖照片，展现合作历程与成果。*

- **高端芯片设计优化取得重大突破**
- **5G及未来无线通信系统节能降本取得重大突破**：在不影响性能前提下，5G基站系统节能降本15-20%
- **网络与智能系统安全**领域合作成效显著
- 获华为公司“网络安全领域2020年度优秀合作伙伴MVP奖”

---

## 第7页：高端芯片设计优化成果

### 高端芯片设计优化成果

*图示：展示两份《杰出合作成果奖》荣誉证书及一份《项目成果应用证明》，证书内容清晰可见。*

- 获2022年华为“杰出合作成果奖”（3000多个项目中选出10个）
- **管晓宏院士**负责的华为海思“序优化技术合作项目”成果优秀，获“杰出合作成果奖”
- **翟桥柱教授**团队的“序优化技术合作项目”同样获“杰出合作成果奖”

**项目成果应用证明核心内容**：

- 项目名称：序优化技术合作，负责人翟桥柱（西安交通大学）
- 核心贡献：解决DSE系统框架收敛慢等问题，相较业界最佳方法，减少30+%迭代计算量和20+%迭代轮数
- 评价：解决芯片设计优化子领域的EDA卡脖子问题，助力打造芯片极致PPA竞争力

---

## 第8页：联合申报成功首批浙江省全省重点实验室

### 联合申报成功首批浙江省全省重点实验室

*图示：右侧为“15家全省重点实验室认定清单”表格，清晰列出各实验室相关信息。*

- 2023年，与海康威视、浙江理工大学共建的“智能物联网络与数据安全”浙江省全省重点实验室获认定，为首批15个之一
- 研究院院长管晓宏院士担任该实验室首席科学家

**清单重点内容（序号9）**：

| 序号 | 实验室名称                           | 依托单位                         | 共建单位                             | 主任 |
| ---- | ------------------------------------ | -------------------------------- | ------------------------------------ | ---- |
| 9    | 全省智能物联网络与数据安全重点实验室 | 杭州海康威视数字技术股份有限公司 | 浙江理工大学，西交网络空间安全研究院 | 王滨 |

---

## 第9页：工作进展

### 工作进展

*图示：通过文字分点列举，清晰呈现各方面工作进展情况。*

| 类别           | 具体进展                                                                                                                                              |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| 技术产业化     | 1. 孵化平台企业，引进3家企业，形成产业生态；2. 与清安优能、浙江清鹤科技达成合作；3. 建立多所高校实习基地；4. 入选中国教育技术协会“网络安全教育基地” |
| 重点项目申报   | 参与国家级科研项目申报，牵头或参与浙江省科技厅“尖兵”重大研发项目，相关项目入围创新成果转化大赛                                                      |
| 国内外学术会议 | 举办或联合主办多个国际、国内学术会议及交流会，促进学术交流与合作                                                                                      |

---

## 第10页：章节过渡

*图示：与目录页类似，高亮显示“网络安全行业思考”部分，起到章节引导作用。*

- 研究院简介
- **网络安全行业思考**
- 技术成果进展
- 定位与服务

---

## 第11页：现状：基于边界的网络安全防护

### 现状：基于边界的网络安全防护

*图示：网络拓扑图展示传统边界防护架构，中心为“防火墙、入侵检测、安全审计”构成的传统边界，连接内外设备与服务，直观呈现防护现状。*

**问题频发！**

**思考：**

- 现有攻击手段有哪些？
- 防护手段有哪些？
- 防护手段的共性问题和边界安全的缺陷在哪里？
- 有没有新型的防护思路可以弥补缺陷？

---

## 第12页：现状：常见攻击方式

### 现状：常见攻击方式

*图示：展示六种常见网络攻击方式及案例对应的示意图，帮助理解不同攻击类型。*

1. **中间人攻击（MITM）**
   - 案例：乌克兰电网攻击事件，OpenSSL的“心脏出血”漏洞
2. **0day/1day/nday攻击**
   - 案例：宝洁公司、多伦多市政府遭攻击，华为Eudemon企业级防火墙攻击等
3. **社会工程学攻击**
   - 案例：5.4亿美金Skymavis加密货币事件，推特大规模账户被黑
4. **单机木马攻击**
   - 案例：西安某医院“USBee”软件
5. **密码爆破攻击**
   - 案例：LinkedIn 1.67亿数据泄露，信用报告机构Equifax数据泄露
6. **DDoS攻击**
   - 案例：巴西多家银行和金融机构，全球最大代码托管平台GitHub遭攻击

---

## 第13页：国内网络安全产业全景

### 国内网络安全产业全景

*图示：左侧条形图显示各细分领域厂商情况，右侧饼图展示各领域营收占比，直观呈现产业格局。*

**产业数据**：

- 510家安全厂商，4941个产品，1051亿元规模

**各领域营收占比**：

- 网络与通信安全20.6%，安全管理与运营8.4%，密码技术及应用7.0%，数据安全7.1%等

**行业洞察**：

1. 产品同质化严重，85%集中在“边界防护”，终端相关厂商和产品少，但恶劣事件超半数源于终端
2. 实现方式多基于操作系统，以软件为主
3. 产值不以技术为导向，厂商竞争激烈
4. 缺乏融合多种防护手段的技术

---

## 第14页：设想：基于芯片的安全管控系统

### 设想：基于芯片的安全管控系统

*图示：通过文字阐述核心设想，明确技术方向与目标。*

> 研发物理层的网络安全管控芯片，构建一体化网络安全管控系统，为“云边端”内外网穿越，物联网安全终端的互联，建立安全通道

1. 从硬件层面保护数据资源免于非授权访问、篡改
2. 建立芯片级安全通信专用通道，实现数据和通道两方面的安全管控

---

## 第15页：安全管控技术理念

### 安全管控技术理念

*图示：系统架构图展示安全管控模组与主CPU及系统的连接，集成认证、审计、监控等功能，呈现技术理念落地架构。*

**核心技术点**：

- 安全启动机制、数据传输加密、设备可控黑白名单、国密硬件加速
- 5W安全策略、专用私有协议、设备智能防拆机、系统“防篡改防拷贝”

---

## 第16页：章节过渡

*图示：与目录页类似，高亮显示“技术成果进展”部分，引导进入技术成果介绍。*

- 研究院简介
- 网络安全行业思考
- **技术成果进展**
- 定位与服务

---

## 第17页：基于专用芯片的安全管控体系

### 基于专用芯片的安全管控体系

*图示：展示从芯片安全体系到技术实现的层次，含芯片型号迭代及相关算法流程图，清晰呈现技术体系架构。*

**1. 芯片安全体系**：

- 芯片固件安全策略、硬件固有安全、CPU自主拓展安全管控指令集等

**2. 核心技术机制**：

- 固有安全存储机制：永久不可更改存贮空间、高限制性写存贮区、用户数据区
- 硬件触发和芯片特权模式，创新性功能区域划分，硬件实现真随机发生及算法加速

**芯片型号迭代**：

- 管控芯片V1：800MHZ 单核/40nm
- 管控芯片V2：1GHZ 双核/40nm
- 管控芯片V2P：1.2GHZ 4核/28nm

---

## 第18页：设备级SoC结构

### 设备级SoC结构

*图示：两张SoC架构图（Fig.1和Fig.2），展示安全管理流程及安全感知SoC系统结构，清晰呈现硬件架构。*

> 非安全核心和安全核心隔离执行架构，通过硬件强制隔离和访问控制机制确保了对CPU、总线、中断和内存等关键模块的“不可绕过、不可更改”安全访问，为可信计算和安全数据处理奠定基础

**Fig.1. Security management in ECCO**：展示从设备固化到云端协同防御的安全管理流程

**Fig.2. SAIAN security-aware SoC system structure**：展示安全感知SoC系统结构，含安全控制协处理器、密码加速器等模块

---

## 第19页：基于硬件、软件协同设计的安全控制

### 基于硬件、软件协同设计的安全控制

*图示：左侧为文字阐述，右侧为安全启动检测过程耗时表格，结合文字与数据呈现技术特点。*

- 提高运行效率的同时保证可迭代、可配置性
- SoC特权模式下的安全启动检测实现全面安全验证链，从硬件锚定的信任根（RoT）开始，验证失败则停止引导并触发安全措施

**安全启动检测过程耗时（部分数据）**：

| 步骤                   | 算法 | 666 MHz (ms) | 1.2 GHz (ms) |
| ---------------------- | ---- | ------------ | ------------ |
| 哈希公钥               | SM2  | 52.45        | 29.07        |
| 使用私钥签名BIN        | SM3  | 511.60       | 283.20       |
| 使用公钥验证签名       | SM3  | 0.03         | 0.02         |
| 加载并执行引导加载程序 | SM2  | 101.28       | 55.38        |
| 总时间                 | -    | 720.36       | 398.15       |

---

## 第20页：双域椭圆曲线密码（ECC）加速器技术

### 双域椭圆曲线密码（ECC）加速器技术

*图示：右侧为“SM2 with Randomized w-NAF”算法流程图，展示算法运行流程与关键环节。*

> 应用于SOC芯片设计，实现统一算术单元，高效处理素数域（P-256/P-544）和二进制域（B-576）操作，具动态切换机制和优化数据路径以实现并行域操作

**抵御侧信道攻击的优势**：

1. 改进k值的NAF编码算法，采用NAF编码随机化算法，与真随机数生成器（TRNG）单元集成，抵抗基于模板的功耗分析攻击
2. 随机化w-NAF使香农熵增加50%，操作序列更难预测，最小熵大于1位时，攻击者难以猜测下一个操作概率

---

## 第21页：PM中的动态存储调整技术

### PM中的动态存储调整技术

*图示：左侧为动态存储调整示意图，右侧为定量分析比较表，直观呈现技术特点与优势。*

> 静态存储对侧信道分析防御有限，该技术采用预计算点的动态存储方案，含存储位置随机化、安全点数据交换及优化内存管理

**定量分析比较**：

| 指标                  | 静态存储方案 (Fixed) | 动态存储方案 (Dynamic) |
| --------------------- | -------------------- | ---------------------- |
| 存储位置熵 (H)        | H = 0                | H = w                  |
| 访问第i个寄存器的概率 | 固定                 | 均匀分布               |
| 抗侧信道攻击能力      | 易受攻击             | 抵抗                   |

**实现方式**：预计算点与寄存器映射定期随机化，计算完成后洗牌器对寄存器数组执行单向循环滚动，更新偏差值，增强安全性

---

## 第22页：混合随机数生成器单元TRNG技术

### 混合随机数生成器单元TRNG技术

*图示：右侧为技术测试P值柱状图，显示不同条件下P值均超过阈值，符合随机性标准。*

> 应用于密钥系统，物理随机性实现设计在SoC中，适用于对称和非对称加密算法

**TRNG方案特点**：

- 内置4路独立随机振荡源，多振荡器组合冗余熵源和纠错机制
- 内置数字线性反馈移位随机数处理器，支持多种自检

**测试结果**：P值均超过阈值，极端温度（80℃）下仍具显著性，通过比例高且波动稳定，符合随机性认证标准

---

## 第23页：核心成果一：安全管控芯片介绍

### 核心成果一：安全管控芯片介绍（赛安二号）

*图示：芯片详细架构图，标注各功能模块，清晰呈现芯片结构与组成。*

- **高处理性能**
  - 4核Cortex-A53, 1.2GHz；单核Cortex-M4F
  - 32KB ICache及32KB DCache，512KB L2 Cache
- **高密文数据吞吐**
  - 算法硬件加速，2Gbps（512Mbps x 4）
  - 支持EtherNet、USB、PCIE等高速输入输出
- **芯片安全体系**
  - RTC、白名单等“不可更改”功能区域划分
  - CPU、总线、中断、内存等模块安全访问“不可绕过”
  - 锁定JTAG调试和efuse检测模式

---

## 第24页：核心成果二：可信安全管控操作系统

### 核心成果二：可信安全管控操作系统

*图示：右侧为架构图，展示“普通世界”与“安全世界”的隔离及切换机制，呈现系统架构。*

> 在CPU上同时隔离运行非安全核（任务核，运行通用操作系统）和安全核（管控核，运行安全操作系统）

**特性**：

- 可信操作系统与通用操作系统的切换
- 私有审计存储开发
- 专用接口授权
- 5W安全管控开发多因素策略授权
- 电源管控方案

---

## 第25页：核心成果三：安全管控私有传输协议

### 核心成果三：安全管控私有传输协议

*图示：右侧展示基于DH密钥交换的流程，呈现密钥交换过程与原理。*

> 基于Diffie-Hellman密钥交换的轻量级传输层安全加密协议，支持多种加密算法，独特支持国密算法，提供完整加密通信、身份验证与完整性保护

**特点**：

- 集成SM2、SM3、SM4国密算法
- 模块化协议框架，可构建定制化安全通信方案
- 双向DH密钥交换机制，支持多种DH函数及加密算法哈希函数

---

## 第26页：核心成果四：安全嵌入式系统固件

### 核心成果四：安全嵌入式系统固件

*图示：流程图展示基于5W的安全策略，清晰呈现各维度策略内容。*

> 支撑实现5个维度安全体系

| 维度                  | 策略                                               |
| --------------------- | -------------------------------------------------- |
| Who (访问者)          | 身份/业务权限/生理特征等认证、授权                 |
| When (访问时间)       | 独立时钟及电源控制，防止非授权                     |
| Where (访问地点)      | 独立电池及北斗定位，防止非授权或意外               |
| Which (访问方式/对象) | 涵盖多种通信方式、地址端口及数据处理方式           |
| What (访问行为/内容)  | 本地记录活动并事后审计；网关进行应用层业务行为审计 |

---

## 第27页：核心成果五：基于AI的网络防御应用

### 核心成果五：基于AI的网络防御应用

*图示：通过文字分点阐述，明确应用场景与功能。*

- **行为分析**：静态文件AI、动态行为AI、行为关联分析，检测防护勒索病毒等，发现异常与恶意活动
- **安全运营**：实时监控网络流量等，自动化渗透测试，提前防御潜在攻击
- **特征库**：自动提取病毒特征，降低更新频率，提高防护灵活性
- **大语言模型应用**：应对网络钓鱼、恶意软件检测等

---

## 第28页：核心成果六：安全管控平台

### 核心成果六：安全管控平台

*图示：含多因素制约流程图、多因素授权方式图及芯片存储区划分图，呈现平台功能与机制。*

- 基于AI的多因素策略授权/审计（2/3/4人等）
- 根据业务岗位需求确定安全策略
- 内部人员、操作系统无法更改安全策略及管控固件，软硬绑定，避免漏洞及人为风险

---

## 第29页：安全信创计算机

### 安全信创计算机

*图示：分左侧可信功能模块与右侧硬件安全功能两部分，清晰呈现计算机安全特性。*

> 内生TCM可信密码及启动检测

**左侧：可信功能模块**

- 安全启动检测：检查固件及操作系统等是否被非法修改
- 认证与授权：确认用户身份，确保授权使用
- 数据加密和解密：硬件加密解密，保护信息安全

**右侧：硬件安全功能**

- 硬件安全监控：检测非法访问和篡改，采取数据销毁等措施
- 建立安全信任根基：提供密钥保护，确保计算平台安全

**可选功能**：系统保护、电子围栏

---

## 第30页：PCI-E 三级密码卡

### PCI-E 三级密码卡

*图示：通过文字分点介绍功能模块与特性，呈现密码卡功能与优势。*

> 高性能可信安全密码卡，支持SM1、SM2等国密算法及AES、RSA等国际算法，提供高速密码运算服务

**主要功能模块**：

- 密钥备份恢复：配合UKEY实现，保证数据传输和存储安全
- 权限管理：用户权限分层管理，支持虚拟化调用
- 三级安全模块特性：更强物理安全机制、安全屏蔽盖等-

---

## 第31页：安全网关

### 安全网关

*图示：展示分站点和中心站系列安全网关设备外观，配合表格呈现性能指标。*

| 性能指标          | 分站点系列                         | 中心站系列                             |
| ----------------- | ---------------------------------- | -------------------------------------- |
| 物理接口          | 4千兆电口，2个千兆Combo，4G内存等  | 标准1U设备，6个千兆电口，4个千兆光口等 |
| SDWAN最大加密吞吐 | 250Mbps                            | 500Mbps                                |
| SSLVPN最大并发    | 100个                              | 300个                                  |
| 防火墙吞吐量      | 3Gbps                              | 10Gbps                                 |
| 七层吞吐量        | 300Mbps                            | 3Gbps                                  |
| 最大并发连接数    | 80万                               | 300万                                  |
| 新建连接数        | 4W/S                               | 10W/S                                  |
| 功能模块          | IPS漏洞防护模块、SD-WAN、VPN模块等 | 同左                                   |

---

## 第32页：手持移动终端

### 手持移动终端

*图示：通过文字阐述终端漏洞及解决方案，明确安全防护措施。*

> 手持移动终端在软件系统和硬件结构存在安全漏洞，可能导致数据泄露、系统瘫痪甚至更恶劣事件，如黎巴嫩传呼机和对讲机爆炸事件

**漏洞示例**：

1. 软件更新不及时
2. 缺乏加密通信
3. 身份认证机制薄弱
4. 缺乏远程管理和监控
5. 硬件结构缺乏防护机制

**解决方案**：

- 防拆机：检测物理拆卸，执行锁定、数据销毁等操作
- 防掉包：多级多因素产品序列识别校验
- 国密传输私有安全通信协议：保障通信安全

---

## 第33页：整体应用框图

### 整体应用框图

*图示：展示整体应用场景框图，呈现终端、通信、管控及平台功能的完整流程。*

**流程**：

1. **终端**：医疗移动、政务办公等场景的赛安安全电脑
2. **通信**：电脑内嵌芯片，通过赛安安全网关通信
3. **管控**：数据传输到赛安安全管控平台
4. **平台功能**：连接安全服务器，提供安全加密等服务
5. **场景**：适用于政务、司法、医疗等领域

---

## 第34页：应用框图：物联网和工业互联网

### 应用框图：物联网和工业互联网

*图示：物联网和工业互联网应用场景框图，呈现终端、通信管控及平台功能的架构。*

**左侧（终端侧）**：

- 赛安安全终端集成在电表、水表等物联网设备，内嵌“赛安一号”芯片

**中间（通信与管控）**：

- 终端通过赛安安全网关通信，数据汇聚到赛安安全管控平台，网关内嵌“赛安二号”芯片

**右侧（平台功能）**：

- 提供访问控制、异常检测等功能

---

## 第35页：章节过渡

*图示：与目录页类似，高亮显示“定位与服务”部分，引导进入相关内容。*

- 研究院简介
- 网络安全行业思考
- 技术成果进展
- **定位与服务**

---

## 第36页：公共服务平台

### 公共服务平台

*图示：左侧三个相互关联的圆环代表技术平台、产业平台、区域平台，右侧为具体服务内容。*

- **技术平台**：资源集约化、服务标准化、响应敏捷化，提供工具链集成等技术支撑
- **产业平台**：上下游协同、产学研融合、创业生态圈，提供威胁情报共享等产业协同服务
- **区域平台**：区域资源聚合、政策精准适配，开展人才培养等生态培育服务

---

## 第37页：实战攻防服务

### 实战攻防服务

*图示：通过文字分点列举攻击方式与服务特点，明确服务内容与优势。*

> 提供全方位网络攻防服务，特别是嵌入式级、设备级攻防，涵盖十余种高级攻击模拟等，全面评估系统安全态势

**攻击方式**（部分）：

- 内存与控制流劫持攻击、虚拟机逃逸攻击、Spectre幽灵漏洞攻击等-

**服务特点**：

- 软硬协同深度：掌握硬件逆向与软件漏洞挖掘能力
- 行业垂直性：适应车规级等严苛场景
- 长周期支持：覆盖设备全生命周期
- 双重视角：融合网络安全与功能安全

---

## 第38页：联系方式

### 期待更多交流

*图示：页面底部附微信公众号二维码，清晰展示联系信息。*

**梁梦雷 13581555659**

- **联系地址**：诸暨市浦阳路18号科创园3#楼
- **联系电话**：0575-87779388
- **网址**：www.csa-xje.edu.cn
- **微信公众号**：（附二维码）
