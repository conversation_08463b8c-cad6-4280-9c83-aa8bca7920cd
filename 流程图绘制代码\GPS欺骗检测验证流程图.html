<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS欺骗检测验证流程图</title>
    <style>
        /*
         * 基于"三位一体"技术体系架构图方法论的流程图实现
         * 核心设计理念：CSS Grid布局 + SVG动态连接 + 循环流程展示
         */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "PingFang SC", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 --- */
            --color-primary: #1e40af; /* 主色调-蓝色 */
            --color-data: #1e40af; /* 数据采集-蓝色 */
            --color-feature: #059669; /* 特征提取-绿色 */
            --color-model: #7c3aed; /* 模型推理-紫色 */
            --color-threat: #ea580c; /* 威胁评估-橙色 */
            --color-decision: #dc2626; /* 决策判断-红色 */
            --color-response: #1e3a8a; /* 响应执行-深蓝 */
            
            --color-border-light: #e5e7eb;
            --color-bg-light: #f9fafb;
            --color-bg-main: #ffffff;
            --color-text-dark: #1f2937;
            --color-text-light: #6b7280;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --shadow-strong: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .flowchart-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-primary);
        }

        .flowchart-container {
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-subtle);
            padding: 40px;
            margin: 0 auto;
            max-width: 1200px;
            width: 100%;
            position: relative;
        }

        .flow-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 60px 40px;
            margin-bottom: 40px;
            position: relative;
            min-height: 400px;
        }

        .flow-step {
            background-color: var(--color-bg-main);
            border: 3px solid var(--color-border-light);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-subtle);
            display: flex;
            flex-direction: column;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .flow-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-strong);
        }

        .flow-step.data-collection {
            border-color: var(--color-data);
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.05) 0%, rgba(30, 64, 175, 0.1) 100%);
        }

        .flow-step.feature-extraction {
            border-color: var(--color-feature);
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.05) 0%, rgba(5, 150, 105, 0.1) 100%);
        }

        .flow-step.model-inference {
            border-color: var(--color-model);
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05) 0%, rgba(124, 58, 237, 0.1) 100%);
        }

        .flow-step.threat-assessment {
            border-color: var(--color-threat);
            background: linear-gradient(135deg, rgba(234, 88, 12, 0.05) 0%, rgba(234, 88, 12, 0.1) 100%);
        }

        .flow-step.decision-making {
            border-color: var(--color-decision);
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(220, 38, 38, 0.1) 100%);
        }

        .flow-step.response-execution {
            border-color: var(--color-response);
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(30, 58, 138, 0.1) 100%);
        }

        .step-title {
            font-family: var(--font-heading);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-text-dark);
        }

        .step-content {
            list-style: none;
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--color-text-light);
            text-align: left;
        }

        .step-content li {
            position: relative;
            margin-bottom: 6px;
            padding-left: 16px;
        }

        .step-content li::before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .data-collection .step-content li::before { color: var(--color-data); }
        .feature-extraction .step-content li::before { color: var(--color-feature); }
        .model-inference .step-content li::before { color: var(--color-model); }
        .threat-assessment .step-content li::before { color: var(--color-threat); }
        .decision-making .step-content li::before { color: var(--color-decision); }
        .response-execution .step-content li::before { color: var(--color-response); }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .indicators-section {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, var(--color-bg-light) 0%, var(--color-bg-main) 100%);
            border-radius: var(--border-radius);
            border: 2px solid var(--color-primary);
        }

        .indicators-title {
            font-family: var(--font-heading);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--color-primary);
            text-align: center;
        }

        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .indicator-item {
            background-color: var(--color-bg-main);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--color-primary);
            box-shadow: var(--shadow-subtle);
        }

        .indicator-label {
            font-weight: 600;
            color: var(--color-text-dark);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .indicator-icon {
            font-size: 1.2rem;
            color: var(--color-primary);
        }

        .indicator-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 4px;
        }

        .indicator-description {
            font-size: 0.85rem;
            color: var(--color-text-light);
            line-height: 1.4;
        }

        .algorithm-info {
            margin-top: 30px;
            padding: 25px;
            background-color: var(--color-bg-main);
            border-radius: var(--border-radius);
            border: 2px solid var(--color-model);
        }

        .algorithm-title {
            font-family: var(--font-heading);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--color-model);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .algorithm-content {
            color: var(--color-text-dark);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .algorithm-highlight {
            background-color: rgba(124, 58, 237, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: var(--color-model);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .flow-grid {
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(3, 1fr);
                gap: 40px 30px;
            }
        }

        @media (max-width: 768px) {
            .flowchart-container {
                padding: 20px;
            }
            
            .flow-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(6, 1fr);
                gap: 30px;
            }
            
            .indicators-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .flow-step {
            animation: fadeInUp 0.6s ease forwards;
        }

        .flow-step:nth-child(1) { animation-delay: 0.1s; }
        .flow-step:nth-child(2) { animation-delay: 0.2s; }
        .flow-step:nth-child(3) { animation-delay: 0.3s; }
        .flow-step:nth-child(4) { animation-delay: 0.4s; }
        .flow-step:nth-child(5) { animation-delay: 0.5s; }
        .flow-step:nth-child(6) { animation-delay: 0.6s; }

        /* 箭头动画 */
        @keyframes arrowFlow {
            0% { stroke-dashoffset: 20; }
            100% { stroke-dashoffset: 0; }
        }

        .flow-arrow {
            stroke-dasharray: 5, 5;
            animation: arrowFlow 2s linear infinite;
        }
    </style>
</head>
<body>
    <div class="flowchart-title">GPS欺骗检测验证流程图</div>
    
    <div class="flowchart-container">
        <div class="flow-grid" id="flowGrid">
            <svg class="connector-canvas" id="connectorCanvas">
                <defs>
                    <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                            markerUnits="strokeWidth" markerWidth="8" markerHeight="6"
                            orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="#374151" />
                    </marker>
                    <marker id="arrowhead-primary" viewBox="0 0 10 10" refX="8" refY="5" 
                            markerUnits="strokeWidth" markerWidth="8" markerHeight="6"
                            orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--color-primary)" />
                    </marker>
                </defs>
            </svg>

            <!-- 第一行：数据采集 → 特征提取 → 模型推理 -->
            <div class="flow-step data-collection" id="dataCollection">
                <div class="step-title">数据采集</div>
                <ul class="step-content">
                    <li>GPS信号</li>
                    <li>惯导数据</li>
                    <li>视觉里程计</li>
                </ul>
            </div>

            <div class="flow-step feature-extraction" id="featureExtraction">
                <div class="step-title">特征提取</div>
                <ul class="step-content">
                    <li>时序特征</li>
                    <li>空间特征</li>
                    <li>频域特征</li>
                </ul>
            </div>

            <div class="flow-step model-inference" id="modelInference">
                <div class="step-title">模型推理</div>
                <ul class="step-content">
                    <li>LSTM处理</li>
                    <li>GNN融合</li>
                    <li>置信评估</li>
                </ul>
            </div>

            <!-- 第二行：响应执行 ← 决策判断 ← 威胁评估 -->
            <div class="flow-step response-execution" id="responseExecution">
                <div class="step-title">响应执行</div>
                <ul class="step-content">
                    <li>告警输出</li>
                    <li>系统切换</li>
                    <li>应急处理</li>
                </ul>
            </div>

            <div class="flow-step decision-making" id="decisionMaking">
                <div class="step-title">决策判断</div>
                <ul class="step-content">
                    <li>阈值比较</li>
                    <li>多级判断</li>
                    <li>联动响应</li>
                </ul>
            </div>

            <div class="flow-step threat-assessment" id="threatAssessment">
                <div class="step-title">威胁评估</div>
                <ul class="step-content">
                    <li>置信度>95%</li>
                    <li>风险等级</li>
                    <li>时间窗口</li>
                </ul>
            </div>
        </div>

        <!-- 关键验证指标 -->
        <div class="indicators-section">
            <div class="indicators-title">关键验证指标</div>
            <div class="indicators-grid">
                <div class="indicator-item">
                    <div class="indicator-label">
                        <span class="indicator-icon">🎯</span>
                        检测准确率
                    </div>
                    <div class="indicator-value">&gt;95%</div>
                    <div class="indicator-description">渐进式欺骗检测准确率，确保高精度威胁识别</div>
                </div>

                <div class="indicator-item">
                    <div class="indicator-label">
                        <span class="indicator-icon">⚡</span>
                        检测延迟
                    </div>
                    <div class="indicator-value">&lt;30秒 (渐进式)，&lt;5秒 (突发式)</div>
                    <div class="indicator-description">快速响应不同类型的GPS欺骗攻击</div>
                </div>

                <div class="indicator-item">
                    <div class="indicator-label">
                        <span class="indicator-icon">🛡️</span>
                        误报率
                    </div>
                    <div class="indicator-value">&lt;2%</div>
                    <div class="indicator-description">正常飞行环境下的误报率控制</div>
                </div>

                <div class="indicator-item">
                    <div class="indicator-label">
                        <span class="indicator-icon">🚀</span>
                        系统响应
                    </div>
                    <div class="indicator-value">&lt;50ms</div>
                    <div class="indicator-description">威胁确认到响应执行的系统延迟</div>
                </div>
            </div>
        </div>

        <!-- 算法技术说明 -->
        <div class="algorithm-info">
            <div class="algorithm-title">
                <span>🧠</span>
                核心算法技术
            </div>
            <div class="algorithm-content">
                本验证流程采用<span class="algorithm-highlight">LSTM+GNN融合架构</span>，通过长短期记忆网络处理时序特征，
                结合图神经网络进行多源数据融合。系统支持<span class="algorithm-highlight">渐进式欺骗检测</span>和
                <span class="algorithm-highlight">突发式欺骗检测</span>两种模式，通过卡尔曼滤波残差分析和
                多源数据交叉验证，实现高精度、低延迟的GPS欺骗检测能力。
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('flowGrid');
            const svg = document.getElementById('connectorCanvas');

            // 连接关系定义
            const connections = [
                // 第一行：从左到右
                { from: 'dataCollection', to: 'featureExtraction', fromPort: 'right', toPort: 'left', type: 'forward' },
                { from: 'featureExtraction', to: 'modelInference', fromPort: 'right', toPort: 'left', type: 'forward' },
                
                // 向下连接：模型推理到威胁评估
                { from: 'modelInference', to: 'threatAssessment', fromPort: 'bottom', toPort: 'top', type: 'down' },
                
                // 第二行：从右到左
                { from: 'threatAssessment', to: 'decisionMaking', fromPort: 'left', toPort: 'right', type: 'backward' },
                { from: 'decisionMaking', to: 'responseExecution', fromPort: 'left', toPort: 'right', type: 'backward' },
                
                // 循环连接：响应执行回到数据采集
                { from: 'responseExecution', to: 'dataCollection', fromPort: 'top', toPort: 'bottom', type: 'cycle' }
            ];
            
            function getPortPosition(element, portSide, containerRect) {
                const elemRect = element.getBoundingClientRect();
                const relX = elemRect.left - containerRect.left;
                const relY = elemRect.top - containerRect.top;
                
                switch(portSide) {
                    case 'top': return { x: relX + elemRect.width / 2, y: relY };
                    case 'bottom': return { x: relX + elemRect.width / 2, y: relY + elemRect.height };
                    case 'left': return { x: relX, y: relY + elemRect.height / 2 };
                    case 'right': return { x: relX + elemRect.width, y: relY + elemRect.height / 2 };
                    default: return { x: relX + elemRect.width / 2, y: relY + elemRect.height / 2 };
                }
            }
            
            function drawConnection(p1, p2, type = 'forward') {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                
                let pathData;
                if (type === 'forward' || type === 'backward') {
                    // 直线连接
                    pathData = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;
                } else if (type === 'down') {
                    // 垂直向下连接
                    pathData = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;
                } else if (type === 'cycle') {
                    // 循环连接：弯曲路径
                    const midX = p1.x - 100;
                    pathData = `M ${p1.x} ${p1.y} Q ${midX} ${p1.y - 50} ${midX} ${(p1.y + p2.y) / 2} Q ${midX} ${p2.y + 50} ${p2.x} ${p2.y}`;
                }

                path.setAttribute('d', pathData);
                path.setAttribute('fill', 'none');
                path.setAttribute('stroke-width', '3');
                path.setAttribute('marker-end', type === 'cycle' ? 'url(#arrowhead-primary)' : 'url(#arrowhead)');
                
                if (type === 'cycle') {
                    path.setAttribute('stroke', 'var(--color-primary)');
                    path.classList.add('flow-arrow');
                } else {
                    path.setAttribute('stroke', '#374151');
                }
                
                svg.appendChild(path);
            }

            function drawConnections() {
                // 清空旧连接线，保留defs
                svg.innerHTML = svg.querySelector('defs').outerHTML;
                const containerRect = container.getBoundingClientRect();
                
                connections.forEach(conn => {
                    const fromEl = document.getElementById(conn.from);
                    const toEl = document.getElementById(conn.to);
                    if (!fromEl || !toEl) return;
                    
                    const p1 = getPortPosition(fromEl, conn.fromPort, containerRect);
                    const p2 = getPortPosition(toEl, conn.toPort, containerRect);
                    drawConnection(p1, p2, conn.type);
                });
            }

            // 添加交互效果
            const steps = document.querySelectorAll('.flow-step');
            steps.forEach(step => {
                step.addEventListener('click', () => {
                    const title = step.querySelector('.step-title').textContent;
                    const items = Array.from(step.querySelectorAll('.step-content li')).map(li => li.textContent);
                    
                    console.log(`${title}:`, items);
                    // 可以添加更多交互功能，如显示详细说明
                });
            });

            const observer = new ResizeObserver(drawConnections);
            window.addEventListener('load', () => {
                setTimeout(drawConnections, 200); // 延迟确保动画完成
                observer.observe(container);
            });
        });
    </script>
</body>
</html>