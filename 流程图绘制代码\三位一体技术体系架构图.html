<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图1-1 "三位一体"技术体系架构图 (精修版)</title>
    <style>
        /*
         * SOPHIA-MIND 精修版优化说明:
         * 1. CSS变量体系化: 核心样式通过变量管理，便于全局调整和主题复用。
         *    - 例如，修改 --color-primary 即可一键更换主品牌色。
         * 2. 精致化视觉元素: 采用1px细边框，层级化的边框颜色，以及更微妙的阴影。
         * 3. 专业化版式布局: 使用gap精确控制层间距，优化卡片内文字行高与边距。
         * 4. 严谨化内容措辞: 统一各模块描述为名词性短语，增强专业性和一致性。
        */
        :root {
            --font-main: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            --font-heading: "Microsoft YaHei", "PingFang SC", var(--font-main);
            
            /* --- 色彩体系 (可在此处一键换肤) --- */
            --color-primary: #005A9C; /* 品牌主色/核心模块边框色 */
            --color-accent: #00A1E4; /* 辅助色，现用于部分列表项符号 */
            --color-border-light: #DEE2E6; /* 次级模块边框色 */
            --color-bg-light: #F8F9FA;
            --color-bg-main: #FFFFFF;
            --color-text-dark: #212529;
            --color-text-light: #495057;

            /* --- 样式细节 --- */
            --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.02);
            --border-radius: 6px;
            --line-color: #5E6C7F; /* 连接线颜色 */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            background-color: var(--color-bg-light);
            color: var(--color-text-dark);
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .flowchart-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
        }

        .flowchart-main-container {
            position: relative;
            width: 1200px;
            height: auto; /* 高度自适应 */
            background-color: var(--color-bg-main);
            padding: 40px;
            display: flex;
            flex-direction: column;
            gap: 40px; /* 精确控制层间距 */
        }

        .layer {
            display: flex;
            align-items: stretch; /* 让卡片等高 */
            gap: 30px;
        }
        
        .top-layer, .interface-layer { justify-content: center; }
        .middle-layer, .bottom-layer { justify-content: space-around; }

        .flow-stage {
            background-color: var(--color-bg-main);
            border: 1px solid var(--color-border-light); /* 默认浅灰细边框 */
            border-radius: var(--border-radius);
            padding: 16px 20px; /* 优化内边距 */
            box-shadow: var(--shadow-subtle); /* 使用更微妙的阴影 */
            display: flex;
            flex-direction: column;
            text-align: center;
        }
        
        /* 核心模块使用主色边框以强调层级 */
        #coreArchitecture {
            border-color: var(--color-primary);
            width: 450px; /* 略微加宽以容纳文字 */
        }
        #hardwareTrust, #cryptoStack, #intelligentAudit { width: 280px; }
        #interfaceLayer { width: 500px; }
        #tacticalRadio, #protocolAdapter, #securityPolicy { width: 280px; }

        .stage-title {
            font-family: var(--font-heading);
            font-size: 1.15rem; /* ~18pt 视觉效果 */
            font-weight: 600;
            margin-bottom: 1rem; /* B增大了标题与正文的间距 */
            color: var(--color-text-dark);
        }

        .stage-content-list {
            list-style: none;
            font-size: 0.9rem; /* ~14pt 视觉效果 */
            line-height: 1.5; /* 优化行高 */
            color: var(--color-text-light);
            text-align: left;
            padding-left: 20px;
        }
        
        .stage-content-list li {
            position: relative;
            margin-bottom: 4px; /* 增加列表项间距 */
        }

        .stage-content-list li::before {
            content: "•";
            position: absolute;
            left: -16px;
            color: var(--color-accent);
        }

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1; /* 确保不影响文本选择 */
        }
    </style>
</head>
<body>
    <div class="flowchart-title">图1-1 "三位一体"技术体系架构图</div>
    
    <div class="flowchart-main-container" id="myFlowchartContainer">
        <svg class="connector-canvas" id="connectorCanvas">
            <defs>
                <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" 
                        markerUnits="strokeWidth" markerWidth="6" markerHeight="4" /* 箭头改小以适应细线 */
                        orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--line-color)" />
                </marker>
            </defs>
        </svg>

        <!-- 顶层 -->
        <div class="layer top-layer">
            <div class="flow-stage" id="coreArchitecture">
                <div class="stage-title">"赛安"安全协处理器核心架构</div>
                <ul class="stage-content-list">
                    <li>三位一体技术体系融合</li>
                    <li>硬件级可信根与安全防护</li>
                    <li>全栈国产化自主可控技术</li>
                </ul>
            </div>
        </div>

        <!-- 中层 -->
        <div class="layer middle-layer">
            <div class="flow-stage" id="hardwareTrust"><div class="stage-title">硬件信任根</div>
                <ul class="stage-content-list">
                    <li>可信安全启动链</li>
                    <li>可信计算执行环境 (TEE)</li>
                    <li>硬件防拆机与环境检测</li>
                    <li>关键数据安全自毁机制</li>
                </ul></div>
            <div class="flow-stage" id="cryptoStack"><div class="stage-title">国密全栈</div>
                <ul class="stage-content-list">
                    <li>国密算法支持 (SM2/3/4)</li>
                    <li>高性能硬件加解密引擎</li>
                    <li>全周期安全密钥管理</li>
                    <li>高速加密性能 (≥2Gbps)</li>
                </ul></div>
            <div class="flow-stage" id="intelligentAudit"><div class="stage-title">智能审计</div>
                <ul class="stage-content-list">
                    <li>AI赋能异常行为检测</li>
                    <li>基于区块链的防篡改审计</li>
                    <li>动态安全与主动防御策略</li>
                    <li>高精度审计准确率 (99.5%)</li>
                </ul></div>
        </div>

        <!-- 接口层 -->
        <div class="layer interface-layer">
            <div class="flow-stage" id="interfaceLayer">
                <div class="stage-title">战术通信系统接口层</div>
                <ul class="stage-content-list"><li>标准化系统适配接口</li><li>多协议栈解析与转换</li><li>统一安全策略管理与分发</li></ul>
            </div>
        </div>

        <!-- 底层 -->
        <div class="layer bottom-layer">
            <div class="flow-stage" id="tacticalRadio"><div class="stage-title">某型战术电台</div>
                <ul class="stage-content-list"><li>嵌入式硬件集成方案</li><li>底层硬件驱动适配</li><li>面向业务的性能优化</li></ul></div>
            <div class="flow-stage" id="protocolAdapter"><div class="stage-title">通信协议适配</div>
                <ul class="stage-content-list"><li>标准/非标协议接口转换</li><li>通信协议栈深度优化</li><li>低延迟高可靠实时通信</li></ul></div>
            <div class="flow-stage" id="securityPolicy"><div class="stage-title">安全策略管理</div>
                <ul class="stage-content-list"><li>基于角色的动态权限控制</li><li>安全策略的远程下发</li><li>终端安全状态实时监控</li></ul></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('myFlowchartContainer');
            const svg = document.getElementById('connectorCanvas');

            const connections = [
                { from: 'coreArchitecture', to: 'hardwareTrust', fromPort: 'bottom', toPort: 'top' },
                { from: 'coreArchitecture', to: 'cryptoStack', fromPort: 'bottom', toPort: 'top' },
                { from: 'coreArchitecture', to: 'intelligentAudit', fromPort: 'bottom', toPort: 'top' },
                { from: 'hardwareTrust', to: 'interfaceLayer', fromPort: 'bottom', toPort: 'top' },
                { from: 'cryptoStack', to: 'interfaceLayer', fromPort: 'bottom', toPort: 'top' },
                { from: 'intelligentAudit', to: 'interfaceLayer', fromPort: 'bottom', toPort: 'top' },
                { from: 'interfaceLayer', to: 'tacticalRadio', fromPort: 'bottom', toPort: 'top' },
                { from: 'interfaceLayer', to: 'protocolAdapter', fromPort: 'bottom', toPort: 'top' },
                { from: 'interfaceLayer', to: 'securityPolicy', fromPort: 'bottom', toPort: 'top' },
            ];
            
            // (已修正错误) 精确获取元素连接点在容器内的坐标
            function getPortPosition(element, portSide, containerRect) {
                const elemRect = element.getBoundingClientRect();
                const relX = elemRect.left - containerRect.left;
                const relY = elemRect.top - containerRect.top;
                
                switch(portSide) {
                    case 'top': return { x: relX + elemRect.width / 2, y: relY };
                    case 'bottom': return { x: relX + elemRect.width / 2, y: relY + elemRect.height };
                    case 'left': return { x: relX, y: relY + elemRect.height / 2 };
                    case 'right': return { x: relX + elemRect.width, y: relY + elemRect.height / 2 };
                    default: return { x: relX + elemRect.width / 2, y: relY + elemRect.height / 2 };
                }
            }
            
            // 绘制1px细直角连接线
            function drawElbowPath(p1, p2) {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const midY = p1.y + (p2.y - p1.y) / 2;
                const d = `M ${p1.x} ${p1.y} V ${midY} H ${p2.x} V ${p2.y}`;

                path.setAttribute('d', d);
                path.setAttribute('stroke', 'var(--line-color)');
                path.setAttribute('stroke-width', '1'); // 1px细线
                path.setAttribute('fill', 'none');
                path.setAttribute('marker-end', 'url(#arrowhead)');
                svg.appendChild(path);
            }

            function drawConnections() {
                svg.innerHTML = svg.querySelector('defs').outerHTML; // 清空旧线，保留defs
                const containerRect = container.getBoundingClientRect();
                connections.forEach(conn => {
                    const fromEl = document.getElementById(conn.from);
                    const toEl = document.getElementById(conn.to);
                    if (!fromEl || !toEl) return;
                    const p1 = getPortPosition(fromEl, conn.fromPort, containerRect);
                    const p2 = getPortPosition(toEl, conn.toPort, containerRect);
                    drawElbowPath(p1, p2);
                });
            }

            const observer = new ResizeObserver(drawConnections);
            window.addEventListener('load', () => {
                drawConnections();
                observer.observe(container);
            });
        });
    </script>
</body>
</html>
